#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import os
import re
import sys
import time
import textwrap
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from colorama import init, Fore, Style
import openai
import requests
import random  # 导入random模块用于随机选择API密钥
from dotenv import load_dotenv
import httpx
import math
import traceback
from dotenv import load_dotenv

from RuleProcessor import RuleProcessor 
from ScoreManager import ScoreManager

# 加载环境变量
load_dotenv()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log", encoding='utf-8', mode='w'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# 全局API配置
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
# 从环境变量中读取默认模型名称
DEFAULT_MODEL = os.getenv("MODEL_NAME", "gemini-2.0-flash-exp")
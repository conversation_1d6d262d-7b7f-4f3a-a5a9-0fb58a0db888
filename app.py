#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import os
import re
import sys
import time
import textwrap
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from colorama import init, Fore, Style
import openai
import requests
import random  # 导入random模块用于随机选择API密钥
from dotenv import load_dotenv
import httpx
import math
import traceback

from RuleProcessor import RuleProcessor 
from ScoreManager import ScoreManager
from TextProcessor import TextProcessor
from LLMInterface import LLMInterface
from EssayGrader import EssayGrader

# 初始化colorama，用于彩色输出
init()

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log", encoding='utf-8', mode='w'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 加载环境变量
# 使用override=True确保每次都重新读取并覆盖已有环境变量
env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
load_dotenv(dotenv_path=env_path, override=True)

# 全局API配置 - 直接从环境变量获取最新值
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
# 从环境变量中读取默认模型名称
DEFAULT_MODEL = os.getenv("MODEL_NAME", "gemini-2.0-flash-exp")

# 尝试下载nltk数据（如果尚未下载）
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='作文自动批改系统')
    parser.add_argument('--rules', '-r', required=True, help='批改规则JSON文件路径')
    parser.add_argument('--essay', '-e', required=True, help='学员作文文件路径')
    parser.add_argument('--title', '-t', help='题目文件路径')
    parser.add_argument('--output', '-o', default='批改结果.txt', help='输出结果文件路径')
    parser.add_argument('--model', '-m', default=DEFAULT_MODEL, help='使用的大模型')
    parser.add_argument('--api-key', '-k', help='API密钥')
    parser.add_argument('--base-url', '-b', help='API基础URL')
    parser.add_argument('--verbose', '-v', action='store_true', help='启用详细输出模式')
    parser.add_argument('--interactive', '-i', action='store_true', help='交互模式')
    parser.add_argument('--debug-log', '-d', default='api_debug.log', help='API调试日志文件路径')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.rules):
        logger.error(f"规则文件 {args.rules} 不存在")
        sys.exit(1)
    
    if not os.path.exists(args.essay):
        logger.error(f"作文文件 {args.essay} 不存在")
        sys.exit(1)
    
    # 处理题目
    title = ""
    if args.title:
        if os.path.exists(args.title):
            with open(args.title, 'r', encoding='utf-8') as f:
                title = f.read().strip()
        else:
            logger.error(f"题目文件 {args.title} 不存在")
            sys.exit(1)
    else:
        # 如果未提供题目文件，从作文文件的第一行获取题目
        with open(args.essay, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            if lines:
                title = lines[0].strip()
                logger.info(f"已从作文文件第一行获取题目: {title}")
            else:
                logger.warning("作文文件为空，无法获取题目")
                title = "未知题目"
    
    # 创建评分器并评分
    grader = EssayGrader(
        title=title,
        rules_file=args.rules,
        essay_file=args.essay,
        model_name=args.model,
        api_key=args.api_key,
        base_url=args.base_url,
        verbose=args.verbose,
        debug_log_file=args.debug_log
    )
    
    # 保存评分结果
    grader.save_result(args.output)
    
    print(Fore.GREEN + f"评分完成！结果已保存到 {args.output}" + Style.RESET_ALL)
    print(Fore.CYAN + f"API调试日志已保存到 {args.debug_log}" + Style.RESET_ALL)
    
    # 如果在交互模式下，显示评分摘要
    if args.interactive:
        print("\n" + grader.score_manager.get_score_summary())


if __name__ == "__main__":
    main() 


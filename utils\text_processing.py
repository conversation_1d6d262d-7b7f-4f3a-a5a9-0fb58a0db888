"""
文本处理工具模块。

提供文本分割、中文数字转换、位置解析等实用函数。
"""
import re
from typing import Dict, List, Tuple, Optional, Union

from constants import CHINESE_NUMBERS, PARAGRAPH_ALIASES, Patterns


def chinese_to_arabic(chinese_num: str) -> int:
    """
    将中文数字转换为阿拉伯数字。
    
    支持一至十的中文表示法，以及繁体中文数字。
    
    Args:
        chinese_num: 中文数字字符串
        
    Returns:
        对应的阿拉伯数字
        
    Raises:
        ValueError: 无法识别的中文数字
    """
    if chinese_num.isdigit():
        return int(chinese_num)
        
    result = 0
    temp = 0
    
    for char in chinese_num:
        if char in CHINESE_NUMBERS:
            number = CHINESE_NUMBERS[char]
            if number == 10:  # 处理"十"
                if temp == 0:  # "十"开头，表示10
                    temp = 1
                result += temp * 10
                temp = 0
            else:
                temp = number
        else:
            raise ValueError(f"无法识别的中文数字: {char}")
    
    # 处理最后剩余的数
    if temp > 0:
        result += temp
    
    return result if result > 0 else temp


def split_paragraphs(text: str) -> List[str]:
    """
    将文本分割为段落列表。
    
    Args:
        text: 待分割的文本
        
    Returns:
        段落列表
    """
    # 移除开头和结尾的空白
    text = text.strip()
    
    # 按空行分割文本
    paragraphs = re.split(r'\n\s*\n', text)
    
    # 返回非空段落
    return [p.strip() for p in paragraphs if p.strip()]


def split_sentences(paragraph: str) -> List[str]:
    """
    将段落文本分割为句子列表。
    
    同时支持中文和英文标点符号。
    
    Args:
        paragraph: 段落文本
        
    Returns:
        句子列表
    """
    if not paragraph or paragraph.isspace():
        return []
    
    # 使用正则表达式分割段落
    segments = re.split(Patterns.SENTENCE_SPLIT, paragraph)
    
    # 重组句子和标点
    sentences = []
    i = 0
    current_sentence = ""
    
    while i < len(segments):
        current_sentence += segments[i]
        
        # 如果下一个是标点符号，将其添加到当前句子并创建新句子
        if i + 1 < len(segments) and re.match(Patterns.SENTENCE_SPLIT, segments[i+1]):
            current_sentence += segments[i+1]
            sentences.append(current_sentence.strip())
            current_sentence = ""
            i += 2  # 跳过已添加的标点
        else:
            i += 1
    
    # 添加最后可能剩余的句子
    if current_sentence and not current_sentence.isspace():
        sentences.append(current_sentence.strip())
    
    return [s for s in sentences if s]


def resolve_paragraph_reference(reference: str, total_paragraphs: int) -> int:
    """
    解析段落引用，返回段落索引。
    
    Args:
        reference: 段落引用描述（如"首段"、"第二段"、"尾段"等）
        total_paragraphs: 总段落数
        
    Returns:
        段落索引（0-based）
        
    Raises:
        ValueError: 无效的段落引用
    """
    # 检查特殊别名
    if reference in PARAGRAPH_ALIASES:
        idx = PARAGRAPH_ALIASES[reference]
        # 处理负索引
        return idx if idx >= 0 else total_paragraphs + idx
    
    # 检查"第N段"格式
    match = re.match(Patterns.PARAGRAPH_POSITION, reference)
    if match:
        num_str = match.group(1)
        try:
            num = chinese_to_arabic(num_str)
            # 转换为0-based索引
            return num - 1
        except ValueError:
            raise ValueError(f"无效的段落数字: {num_str}")
    
    raise ValueError(f"无法解析的段落引用: {reference}")


def parse_sentence_position(position_desc: str, current_paragraph: int = 0) -> Tuple[int, int]:
    """
    解析句子位置描述，返回段落和句子索引。
    
    Args:
        position_desc: 位置描述（如"第三句"、"该段第一句"等）
        current_paragraph: 当前段落索引（默认为0）
        
    Returns:
        包含段落索引和句子索引的元组 (paragraph_index, sentence_index)
        
    Raises:
        ValueError: 无效的位置描述
    """
    # 检查"第N句"格式
    match = re.match(Patterns.SENTENCE_POSITION, position_desc)
    if match:
        num_str = match.group(1)
        sentence_index = chinese_to_arabic(num_str) - 1  # 转换为0-based
        return (current_paragraph, sentence_index)
    
    # 检查"该段第N句"或"本段第N句"格式
    match = re.match(Patterns.RELATIVE_POSITION, position_desc)
    if match:
        num_str = match.group(2)
        sentence_index = chinese_to_arabic(num_str) - 1  # 转换为0-based
        return (current_paragraph, sentence_index)
    
    raise ValueError(f"无法解析的句子位置: {position_desc}") 
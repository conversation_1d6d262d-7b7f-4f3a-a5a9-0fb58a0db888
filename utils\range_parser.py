"""
范围描述解析器模块。

提供解析文本范围描述的功能，支持各种格式的范围表达式。
"""
import re
from typing import List, Dict, Tuple, Optional, Union, Any
from dataclasses import dataclass

from constants import TextSeparators, PARAGRAPH_ALIASES
from utils.text_processing import chinese_to_arabic, parse_sentence_position


@dataclass
class RangeNode:
    """表示范围表达式的语法树节点。"""
    type: str  # 节点类型: 'paragraph', 'sentence', 'rule_reference', 'sequential', 'parallel', 'full_text'
    value: Any = None  # 节点值，根据类型不同而不同
    children: List['RangeNode'] = None  # 子节点列表（对于复合节点）
    
    def __post_init__(self):
        """初始化默认值。"""
        if self.children is None and self.type in ['sequential', 'parallel']:
            self.children = []


class RangeParser:
    """
    范围描述解析器。
    
    将自然语言范围描述解析为结构化表示，支持各种范围表达式。
    """
    
    def __init__(self, text_separators: Optional[TextSeparators] = None):
        """
        初始化解析器。
        
        Args:
            text_separators: 文本分隔符配置，若为None则使用默认值
        """
        self.separators = text_separators or TextSeparators()
    
    def parse(self, range_desc: str) -> RangeNode:
        """
        解析范围描述，返回语法树。
        
        Args:
            range_desc: 范围描述字符串
            
        Returns:
            表示解析结果的语法树根节点
            
        Raises:
            ValueError: 无法解析的范围描述
        """
        # 检查完整文本关键词
        if range_desc in ["全文", "整篇作文", "全部内容"]:
            return RangeNode(type="full_text")
        
        # 检查标题关键词
        if range_desc in ["标题", "题目"]:
            return RangeNode(type="title")
        
        # 检查并列范围（使用分号、顿号等分隔）
        for sep in self.separators.parallel_range_separators:
            if sep in range_desc:
                sub_ranges = range_desc.split(sep)
                if len(sub_ranges) > 1:
                    node = RangeNode(type="parallel")
                    for sub_range in sub_ranges:
                        if sub_range.strip():  # 非空子范围
                            node.children.append(self.parse(sub_range.strip()))
                    return node
        
        # 检查递进范围（使用逗号分隔）
        for sep in self.separators.sequential_range_separators:
            if sep in range_desc:
                parts = range_desc.split(sep, 1)  # 只分一次，得到主范围和子范围
                if len(parts) > 1:
                    node = RangeNode(type="sequential")
                    node.children.append(self.parse(parts[0].strip()))  # 主范围
                    node.children.append(self.parse(parts[1].strip()))  # 子范围
                    return node
        
        # 检查段落引用
        if range_desc in PARAGRAPH_ALIASES or re.match(r'^第[一二三四五六七八九十\d]+段$', range_desc):
            return RangeNode(type="paragraph", value=range_desc)
        
        # 检查句子引用
        match = re.match(r'^第[一二三四五六七八九十\d]+句$', range_desc)
        if match:
            return RangeNode(type="sentence", value=range_desc)
        
        # 检查相对句子引用
        match = re.match(r'^(该|本)段第[一二三四五六七八九十\d]+句$', range_desc)
        if match:
            return RangeNode(type="relative_sentence", value=range_desc)
        
        # 检查规则引用
        match = re.match(r'规则([一二三四五六七八九十\d]+)的?句子', range_desc)
        if match:
            rule_num = chinese_to_arabic(match.group(1))
            return RangeNode(type="rule_reference", value=rule_num)
        
        # 无法识别的范围描述
        raise ValueError(f"无法解析的范围描述: {range_desc}")
    
    def extract_text(self, node: RangeNode, text_processor, current_module: str = None, rule_index: int = 0) -> str:
        """
        根据解析结果提取文本。
        
        Args:
            node: 语法树节点
            text_processor: 文本处理器对象（需实现相应的方法）
            current_module: 当前规则模块名
            rule_index: 当前规则索引
            
        Returns:
            提取的文本内容
            
        Raises:
            ValueError: 提取文本时出错
        """
        if node.type == "full_text":
            return text_processor.essay_text
        
        if node.type == "title":
            return text_processor.title
        
        if node.type == "paragraph":
            paragraph_index = text_processor.resolve_paragraph_reference(node.value)
            if 0 <= paragraph_index < len(text_processor.paragraphs):
                return text_processor.paragraphs[paragraph_index]
            raise ValueError(f"段落索引超出范围: {paragraph_index}")
        
        if node.type == "sentence" or node.type == "relative_sentence":
            paragraph_index, sentence_index = parse_sentence_position(
                node.value, 
                current_paragraph=text_processor.current_paragraph_index
            )
            try:
                return text_processor.get_sentence_by_position(paragraph_index, sentence_index)
            except IndexError:
                raise ValueError(f"句子索引超出范围: 段落 {paragraph_index}, 句子 {sentence_index}")
        
        if node.type == "rule_reference":
            rule_sentence = text_processor.find_matching_sentence_for_rule(
                current_module, node.value, text_processor.essay_text
            )
            if not rule_sentence:
                raise ValueError(f"未找到规则 {node.value} 匹配的句子")
            return rule_sentence
        
        if node.type == "parallel":
            # 合并并列范围的文本
            result = []
            for child in node.children:
                try:
                    child_text = self.extract_text(
                        child, text_processor, current_module, rule_index
                    )
                    if child_text:
                        result.append(child_text)
                except ValueError:
                    # 忽略无法提取的子范围
                    pass
            return "\n".join(result)
        
        if node.type == "sequential":
            # 先提取主范围，然后在主范围内提取子范围
            if len(node.children) < 2:
                raise ValueError("递进范围需要至少两个子范围")
            
            main_text = self.extract_text(
                node.children[0], text_processor, current_module, rule_index
            )
            
            # 创建临时文本处理器处理子范围
            temp_processor = type(text_processor)(main_text)
            temp_processor.current_paragraph_index = 0
            
            return self.extract_text(
                node.children[1], temp_processor, current_module, rule_index
            )
        
        # 未知节点类型
        raise ValueError(f"未知的节点类型: {node.type}") 
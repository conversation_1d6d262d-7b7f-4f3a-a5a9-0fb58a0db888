narwhals-1.32.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
narwhals-1.32.0.dist-info/METADATA,sha256=tTNPQd-a1_IScNxv8I2eDKHbLPScOUEXcJj7iNotDuc,9224
narwhals-1.32.0.dist-info/RECORD,,
narwhals-1.32.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
narwhals-1.32.0.dist-info/licenses/LICENSE.md,sha256=heMD6hta6RzeBucppx59AUCgr_ukRY0ABj0bcrN3mKs,1071
narwhals/__init__.py,sha256=l7-CYdaoZF7aBqksi-3-MiIfFZzhJhGBDXMHNtgLuLQ,4560
narwhals/__pycache__/__init__.cpython-312.pyc,,
narwhals/__pycache__/_expression_parsing.cpython-312.pyc,,
narwhals/__pycache__/_translate.cpython-312.pyc,,
narwhals/__pycache__/dataframe.cpython-312.pyc,,
narwhals/__pycache__/dependencies.cpython-312.pyc,,
narwhals/__pycache__/dtypes.cpython-312.pyc,,
narwhals/__pycache__/exceptions.cpython-312.pyc,,
narwhals/__pycache__/expr.cpython-312.pyc,,
narwhals/__pycache__/expr_cat.cpython-312.pyc,,
narwhals/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/__pycache__/expr_list.cpython-312.pyc,,
narwhals/__pycache__/expr_name.cpython-312.pyc,,
narwhals/__pycache__/expr_str.cpython-312.pyc,,
narwhals/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/__pycache__/functions.cpython-312.pyc,,
narwhals/__pycache__/group_by.cpython-312.pyc,,
narwhals/__pycache__/schema.cpython-312.pyc,,
narwhals/__pycache__/selectors.cpython-312.pyc,,
narwhals/__pycache__/series.cpython-312.pyc,,
narwhals/__pycache__/series_cat.cpython-312.pyc,,
narwhals/__pycache__/series_dt.cpython-312.pyc,,
narwhals/__pycache__/series_list.cpython-312.pyc,,
narwhals/__pycache__/series_str.cpython-312.pyc,,
narwhals/__pycache__/series_struct.cpython-312.pyc,,
narwhals/__pycache__/this.cpython-312.pyc,,
narwhals/__pycache__/translate.cpython-312.pyc,,
narwhals/__pycache__/typing.cpython-312.pyc,,
narwhals/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_arrow/__pycache__/__init__.cpython-312.pyc,,
narwhals/_arrow/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_arrow/__pycache__/expr.cpython-312.pyc,,
narwhals/_arrow/__pycache__/group_by.cpython-312.pyc,,
narwhals/_arrow/__pycache__/namespace.cpython-312.pyc,,
narwhals/_arrow/__pycache__/selectors.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_list.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_str.cpython-312.pyc,,
narwhals/_arrow/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_arrow/__pycache__/typing.cpython-312.pyc,,
narwhals/_arrow/__pycache__/utils.cpython-312.pyc,,
narwhals/_arrow/dataframe.py,sha256=IuZb1SAI2mR2OEAs-ZiMykwvuWtbnlNgvtMPR0nJY0E,30772
narwhals/_arrow/expr.py,sha256=yQKQOVCeWzJjLUAtk10E900VYk6aQnwecZNaS92Ec94,8536
narwhals/_arrow/group_by.py,sha256=BSrjwCRxREXFntiS1pP4OOvhxzqilWn9BenhEqAT5ik,6072
narwhals/_arrow/namespace.py,sha256=HKyk4_0Y67SiMZ-LNTvbrXGdXbiJn3ryYhDrcDLM2-o,11139
narwhals/_arrow/selectors.py,sha256=hbmnDVtE37Q4Xmd_rc5ASQFh6c-yH1I7_7E_jQjTjHo,1802
narwhals/_arrow/series.py,sha256=ViyAU2y-43iF6xDLp3gMlSLeJbp50n7e2wtcaEzaWU8,44469
narwhals/_arrow/series_cat.py,sha256=WKLzMjSWIVNx1JAHaQt3R2QjMi8eTC6Ok2frKnPmoVE,644
narwhals/_arrow/series_dt.py,sha256=jKZTHjX7fuE-FS8Tthr1sgDQ2RGKP9LvFtJo_ZuWBvI,7942
narwhals/_arrow/series_list.py,sha256=wSuWm5sGmBGSTdKYIn2hY6rWuAhJy0phY7bHgaSf8IA,467
narwhals/_arrow/series_str.py,sha256=UUZHzyXKI5t4gXh9W7sTmAsP_Aft3frg3FDA90-DYeM,2694
narwhals/_arrow/series_struct.py,sha256=MxbMheMAW_LENypMqRsKpPFOaaJInYgYc7UEpSWhFBE,456
narwhals/_arrow/typing.py,sha256=vfSLyR-Q-UBatOiwfT_89Bad98gosmlTHKvN4eOiWM4,1699
narwhals/_arrow/utils.py,sha256=02AeqR34C72lyPnt9BCPneR1w37hVBLqZ36ZKD1jn7U,20053
narwhals/_compliant/__init__.py,sha256=BlomTDlNL4S8P7UgNa5VvVpGZdUwhmdFcbnxaC9QOw4,2674
narwhals/_compliant/__pycache__/__init__.cpython-312.pyc,,
narwhals/_compliant/__pycache__/any_namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_compliant/__pycache__/expr.cpython-312.pyc,,
narwhals/_compliant/__pycache__/group_by.cpython-312.pyc,,
narwhals/_compliant/__pycache__/namespace.cpython-312.pyc,,
narwhals/_compliant/__pycache__/selectors.cpython-312.pyc,,
narwhals/_compliant/__pycache__/series.cpython-312.pyc,,
narwhals/_compliant/__pycache__/typing.cpython-312.pyc,,
narwhals/_compliant/__pycache__/when_then.cpython-312.pyc,,
narwhals/_compliant/any_namespace.py,sha256=Wxkg_Za2ycC__fkWx9QUB8i1ZEgts1v3LcEYWSReDaE,3327
narwhals/_compliant/dataframe.py,sha256=0oEFd734R0B34s5zbMLxcnkrbfBOAHYwoXbUPC6g5a8,11371
narwhals/_compliant/expr.py,sha256=muIRaoNfoACbgEgeROPHe_zkYGVihX48vTU-3yjz630,38560
narwhals/_compliant/group_by.py,sha256=OdhwH33aJVYeGrWeJ0IDwGrjBDWEqaoJdEOskoMl4zU,5838
narwhals/_compliant/namespace.py,sha256=2fPbgqyrgY--aTFosAH0mr8qE-HKSxdvckdMsWvQWoE,4237
narwhals/_compliant/selectors.py,sha256=sVA-b9ruMijGKlvxy0Vi9FCwfNyr-lwANJ8bm6N6dQA,11660
narwhals/_compliant/series.py,sha256=d-Tl7xGEL26W4uKoQ62utcUqte5HgjfzaOGMk_ciJCY,9349
narwhals/_compliant/typing.py,sha256=1vcqf6yI1H-xquoZx9jskZSNzO7QfiURJm6YJeEimiU,4372
narwhals/_compliant/when_then.py,sha256=fX6xkIJe62wSU31I7oOldKNvkI7QRac8iduHBN2iBF0,6164
narwhals/_dask/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_dask/__pycache__/__init__.cpython-312.pyc,,
narwhals/_dask/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_name.cpython-312.pyc,,
narwhals/_dask/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_dask/__pycache__/group_by.cpython-312.pyc,,
narwhals/_dask/__pycache__/namespace.cpython-312.pyc,,
narwhals/_dask/__pycache__/selectors.cpython-312.pyc,,
narwhals/_dask/__pycache__/utils.cpython-312.pyc,,
narwhals/_dask/dataframe.py,sha256=t9AbE5Sr_Z8hvXRDZymPEf4sGg8VH6Og4iTT1VM18J0,16478
narwhals/_dask/expr.py,sha256=Lyij0x9GnP2HJgEZT6SfqTz0Ph5lBgPsPBh6kQk3xYI,24590
narwhals/_dask/expr_dt.py,sha256=foqDi3vBkPUxE_O8Biz1vWOGJbQ_x19sk2FmIRiVGJI,6169
narwhals/_dask/expr_name.py,sha256=5G8FTY6vVbsqp5QBjbRQ-i2aERuhOTmEkmu4OpXCa9o,2349
narwhals/_dask/expr_str.py,sha256=wnDZjR8MQDk3_e1NWscnOqDATrOLhPUQTzRdvVpDd5M,3469
narwhals/_dask/group_by.py,sha256=oYQzzCKxNdqi5K7-MPN-QTN-SKTpX-7-8uPe2giusCg,4090
narwhals/_dask/namespace.py,sha256=V1HvAgqwmImwB1RbNR6giQapCeNhJ7KhGFLqMLnUCXc,12868
narwhals/_dask/selectors.py,sha256=KNZjF35eVitVqwvT-7_dtZ22Rylu5_uE5UZNd1E3tNQ,1936
narwhals/_dask/utils.py,sha256=-Q6IBeTc2wOgQh_dZ9xceETF2CCWOayrRsbCeQn06yU,6538
narwhals/_duckdb/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_duckdb/__pycache__/__init__.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_name.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/group_by.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/namespace.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/selectors.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/series.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/typing.cpython-312.pyc,,
narwhals/_duckdb/__pycache__/utils.cpython-312.pyc,,
narwhals/_duckdb/dataframe.py,sha256=T_MSw6TpyIB4TufulBBOZRA--iKanByOdKrp6t_VSQ0,18866
narwhals/_duckdb/expr.py,sha256=4EC5_326WEyUxdjVy_ThM5DhKNWSXR7R4SMyclIc5s8,23432
narwhals/_duckdb/expr_dt.py,sha256=LLAAA-fVqfzeVGudR0jOzAKTbxJxj4RJWXwU_uk0Tcw,3958
narwhals/_duckdb/expr_list.py,sha256=IPxeIcKF0sD2-M9ZUolFxPpKSfDAMvk3BFSapcdbXP4,500
narwhals/_duckdb/expr_name.py,sha256=RZz3G8ezuF4NOVqAsYPIJEjqKYv_21GRB7eWAu7_gv4,2187
narwhals/_duckdb/expr_str.py,sha256=caNQGz3h6RPhUciYYVmP_DZqG5MCJzs8L9kB_jdffLI,3906
narwhals/_duckdb/expr_struct.py,sha256=B8rAuCNBr83cRPsSoDD3OfiftLHR3E4yaf859P6QUk8,589
narwhals/_duckdb/group_by.py,sha256=HjmyojTrztxaYD14pbuNfi0EsxzjhSQv3IZ-6ar5N18,1031
narwhals/_duckdb/namespace.py,sha256=BTz5FG4ukYIsxVfUgHDRp2cLPZSUKWrmfQgxbH3j8tA,9563
narwhals/_duckdb/selectors.py,sha256=h1f7wtFRO0sL5Mm_mA8FauScqo20QAI1mYBqV4wLC1Q,1675
narwhals/_duckdb/series.py,sha256=nnmFyiwcygTBRdta-3FlELxaxpshW9Rk-DrSLEnD_NU,1356
narwhals/_duckdb/typing.py,sha256=ARFvdLw78qRHWiHJUVMa1DiVilIh0d2rHz-tDXTwvsE,306
narwhals/_duckdb/utils.py,sha256=i17yICduLAf0lECcRMLOmcZJ2Y0qV2hfKERwNQR8KK8,7903
narwhals/_expression_parsing.py,sha256=sWKpJ_gozntqAH8yuMAHdZNuatKmXPLR2u9QpJv7JD4,15008
narwhals/_ibis/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_ibis/__pycache__/__init__.cpython-312.pyc,,
narwhals/_ibis/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_ibis/__pycache__/series.cpython-312.pyc,,
narwhals/_ibis/dataframe.py,sha256=X8GMEuPecxnET7MjUZdz2AwLR4vnMdty3vrndJRY2is,5517
narwhals/_ibis/series.py,sha256=FBWZ2a7ak8mdN-w0s_eo0iTDWVQWLrxqqRB7D5R_GzA,1250
narwhals/_interchange/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_interchange/__pycache__/__init__.cpython-312.pyc,,
narwhals/_interchange/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_interchange/__pycache__/series.cpython-312.pyc,,
narwhals/_interchange/dataframe.py,sha256=HFmJqUNkV35Rlxb2Gr53qL3_PD2b7oF6mOXUOA0wjmM,6666
narwhals/_interchange/series.py,sha256=QVQ1DoSSjD5k8ssfUztgO3DioP5WORhpZAn6-GdgxcA,1701
narwhals/_pandas_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_pandas_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_cat.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_dt.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_list.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_str.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/series_struct.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_pandas_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_pandas_like/dataframe.py,sha256=zBoa8sZOZDQUgIsAcs3yXQnkN3ART808ewuWgGrfWLQ,42223
narwhals/_pandas_like/expr.py,sha256=0QvFTRic_O2XJoF12noNhYx-npO_befr6iBtYJ5CBWw,12635
narwhals/_pandas_like/group_by.py,sha256=1oZlASVmiOACw45EalXSKYuBgvHWOcdaNPcv5aAhtN8,13430
narwhals/_pandas_like/namespace.py,sha256=z4SqXE4-PbLsovswdkzHY-SetW18wmr125EcM5_CBWI,12520
narwhals/_pandas_like/selectors.py,sha256=H9LoQOjRauUvTgc3Wl8ftt1y9h3Z_dLR9F9rvvyO3nA,2121
narwhals/_pandas_like/series.py,sha256=pHZvN4Sv3oo1YYZ1fotFOZtN-FdcCi1x8Gs4wWA7oWQ,42607
narwhals/_pandas_like/series_cat.py,sha256=LCggb6eWVO6iPuMkxiP2F_83Xa_DrtGomKIi5PCvEqg,563
narwhals/_pandas_like/series_dt.py,sha256=dBG_Vq0FhEI__wCGqp65xEQg9iJLBJ62-rf3gatzFAM,10105
narwhals/_pandas_like/series_list.py,sha256=irZZhYk2Tek48a374DPLV-NSXZ2ZskO1iwz70yy1FfI,2044
narwhals/_pandas_like/series_str.py,sha256=f_8JkmRKwuPAAC4vkfcY3yww_ZNr5a0UFRFoNORNLJ8,4728
narwhals/_pandas_like/series_struct.py,sha256=_9CZiwxDh1-Oh7Bl029_nXKjkXiYzT7iJuKYszpLbTw,721
narwhals/_pandas_like/typing.py,sha256=lGp2OLxQOenrjcopRdg94cmYmIsxUmnfP1hhmodoTYE,518
narwhals/_pandas_like/utils.py,sha256=uMDH2I7mWtQR4kTljs7CPkpwwywDW1evoW9oAJZpKN8,30870
narwhals/_polars/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_polars/__pycache__/__init__.cpython-312.pyc,,
narwhals/_polars/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_polars/__pycache__/expr.cpython-312.pyc,,
narwhals/_polars/__pycache__/group_by.cpython-312.pyc,,
narwhals/_polars/__pycache__/namespace.cpython-312.pyc,,
narwhals/_polars/__pycache__/series.cpython-312.pyc,,
narwhals/_polars/__pycache__/typing.cpython-312.pyc,,
narwhals/_polars/__pycache__/utils.cpython-312.pyc,,
narwhals/_polars/dataframe.py,sha256=eFpUxIaGtk82TVMK8T_yssFp9gn2um0P0Xo3r_mcYZE,23802
narwhals/_polars/expr.py,sha256=mTmSSfhcyxB6SW1zYsLSWXWNeYpXqh8C5H-A3quSlS0,15438
narwhals/_polars/group_by.py,sha256=MhJE1pElrZk5LYucHG-qzgr9KhDo2DeURUKCkmZxZ2M,2177
narwhals/_polars/namespace.py,sha256=6bcnQMPB4ba1oYT6ZZq4sI6nRRYfY8EtMijgwGkzcQo,9118
narwhals/_polars/series.py,sha256=_kb1oV8v45lIQSatKvpmirNDo1bqHA4TS2WfOvb1rf0,24894
narwhals/_polars/typing.py,sha256=ZZ0sTO5nLLsznnGzBkqYVehS291uGoUyXhc1dAXSBys,488
narwhals/_polars/utils.py,sha256=1EraOg9dxp-S4hkDIESma4SOQJmh2YZuPPkWvPJzrcA,9252
narwhals/_spark_like/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/_spark_like/__pycache__/__init__.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/dataframe.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_dt.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_list.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_name.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_str.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/expr_struct.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/group_by.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/namespace.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/selectors.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/typing.cpython-312.pyc,,
narwhals/_spark_like/__pycache__/utils.cpython-312.pyc,,
narwhals/_spark_like/dataframe.py,sha256=KjmcxZh_Z6ZnvSnO7Jlr3VDffiq1D8uN5ITSkHKb6HU,19547
narwhals/_spark_like/expr.py,sha256=trgQhSQSxBWCrEwehLAlBnXOy2Zmc7PgBPbvdob-Sws,24455
narwhals/_spark_like/expr_dt.py,sha256=GKdPQCpqqOk0vw8FvICfi_zeJ_6Ixes4CCVNSh23JKM,2440
narwhals/_spark_like/expr_list.py,sha256=4zXzhU9i6mNOzm5vj8ZBJvBicNKEVjY5Ws9y82r3_Y4,441
narwhals/_spark_like/expr_name.py,sha256=bk39DdKEcHm4KHjaULQGN5RA-klZMHfSzMINOGMDzlQ,2281
narwhals/_spark_like/expr_str.py,sha256=fWYMPmLH-omhxxg2PeXgcjJLgdZ6-ClEAIjeDKt4aRs,5761
narwhals/_spark_like/expr_struct.py,sha256=YFU9mt4h-GDXW2HP0Vg2u_xJXpzGPD8P0iEwITdnQkA,568
narwhals/_spark_like/group_by.py,sha256=CRUtRr0ddXnvpc8cIWFfLU9vYeVd9FBaCLb9OvnzsHk,1164
narwhals/_spark_like/namespace.py,sha256=1YL1aTtosndqbGLOjTZ2sOa7xDSWV-KpcA-WnipHgyk,10801
narwhals/_spark_like/selectors.py,sha256=0dv63uPJkeDDv7Z0WjGy0uYqk7NkTRRiKgmS-Z5Y9t4,1801
narwhals/_spark_like/typing.py,sha256=di49Xy-MuPObGoQqROLxZ2LrvPk07yoEl4T_8Rp27Lk,325
narwhals/_spark_like/utils.py,sha256=scrDHT4iYUQcbKtDRosgEaj2V8_bI5tDJ9ZGXnJfHvE,9183
narwhals/_translate.py,sha256=wEke3nRE5m1UK4H9bfebhL6ypHz53eDLZXwveVE32XM,1994
narwhals/dataframe.py,sha256=ZOqQftpmgvTb0wCvDU_zruLb7yM3zmhDmUCYQkiBrLs,125126
narwhals/dependencies.py,sha256=1icnFTxQTgMCbKHwZPvObG4Yi5NORvzY9NWu3gbYT2o,14515
narwhals/dtypes.py,sha256=H7sCsKOGPzzaRVVVLnMgnDvLtREol670XuwkILMHUZo,21384
narwhals/exceptions.py,sha256=PXwjgNb-BIZfp3pIBVIBV1-IVBKeOKIg9Aoff2QivrM,4462
narwhals/expr.py,sha256=BTQb-EfqLDaCP81IZ41OgHokT-bYpm0mruYevjLo-K0,105449
narwhals/expr_cat.py,sha256=a2DUJKIqv_QU4f55AgnVuirl7xXRgjmHfRQ51dYkuZA,1455
narwhals/expr_dt.py,sha256=8kHamIfgxR50GiKKVJslg55Ujtj-kmoCcViP6l9wq-w,29536
narwhals/expr_list.py,sha256=U_3cl6nOFy8VozxiZkLzO8kykwyge-OP_e02kNRt8G0,1884
narwhals/expr_name.py,sha256=WmWLJBwXLHaxSy85kKj-ngMICOBEpVB3ur3ScUV9OVs,6292
narwhals/expr_str.py,sha256=BGx1nRuz0Zq06IHjc6g2o8OR4qKZtBZiR9PfZBFp3AE,18297
narwhals/expr_struct.py,sha256=YcN4YwMgsnETmW18l_O4dGUF7XCk_4Zhx2nBy3KJ6ZM,2034
narwhals/functions.py,sha256=1UYFuDMquzh9dzem3MblkXgzcOaoGwv38pzP1f9koC0,73869
narwhals/group_by.py,sha256=itnHJcmPiZVgJekk9IfdpUP7CldOIauv1fXBQ271XzY,7295
narwhals/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
narwhals/schema.py,sha256=AZ6TJEmCjNDkcBjgqraL0ak3rzag_o3ruuCcx4rzfEE,6818
narwhals/selectors.py,sha256=0Sxwb93utQUqgQgayEefPDvDlYts0xlDmMXWgYN2ixY,11057
narwhals/series.py,sha256=9TRxDH8qNtoBa2VjGMF4zQxCx6rWMpE_gS9WxtdthKE,88347
narwhals/series_cat.py,sha256=e_pPoZeh1DnR_21ujRA36NUoVzsJ14NbcA_FQCWyt_8,1125
narwhals/series_dt.py,sha256=Pi61wW5bOTXZtdeHngQGQPGlAU4TLJbnr5bYCQoEzEI,23359
narwhals/series_list.py,sha256=Lfv2OK86mAuXH6cgdpO1sDaHBZ5QGAzowvHjQtkccvQ,1256
narwhals/series_str.py,sha256=Zx11jZmnZ_AFTrgjoLcqDAOQPJJZIW5kZaHqPCl7eAI,14996
narwhals/series_struct.py,sha256=rqs8PjzoUgBGmkjkzg6froBsV3b4czFQSJk46BY6A6Q,1259
narwhals/stable/__init__.py,sha256=b9soCkGkQzgF5jO5EdQ6IOQpnc6G6eqWmY6WwpoSjhk,85
narwhals/stable/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__init__.py,sha256=35_ZEo2pHrjKOsPh_rltEeXk-GIuuysR7SlCqcOS4Qc,90012
narwhals/stable/v1/__pycache__/__init__.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/_dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dependencies.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/dtypes.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/selectors.cpython-312.pyc,,
narwhals/stable/v1/__pycache__/typing.cpython-312.pyc,,
narwhals/stable/v1/_dtypes.py,sha256=B4oJZYatevmBTbYodyrcyMMMt3dWxgiZeDP9ZBACT2E,2941
narwhals/stable/v1/dependencies.py,sha256=PqYYOP_8w0ZJraajFnpYwc_ZdURRQIcgqplKsnymL_U,2204
narwhals/stable/v1/dtypes.py,sha256=C20rFSp455WJtGfaCZzxtUg600fPVZzIh9iAaz7bc40,2229
narwhals/stable/v1/selectors.py,sha256=4qDY5ZC6NyavSG6tK2M7kC1jOPSXJOltNzdgEx_Gu2Y,485
narwhals/stable/v1/typing.py,sha256=WkQc0LavXsuMaThJjRENA2YGu3W3RFEin9yuMk9kcM0,7054
narwhals/this.py,sha256=BbKcj0ReWqE01lznzKjuqq7otXONvjBevWWC5aJhQxs,1584
narwhals/translate.py,sha256=dCG32PLpWl5waPKOpo0vLkaY4iU87ZhiCWyvUiXMt7E,35554
narwhals/typing.py,sha256=T_ChuqQjWgpXH_LL2LoJU33DwNkGpGBCdfChIw85ALE,8471
narwhals/utils.py,sha256=CPVGMArScOC7_ZWFIdryBk5E7Mrv23V7gQv6f-e5VDQ,55641

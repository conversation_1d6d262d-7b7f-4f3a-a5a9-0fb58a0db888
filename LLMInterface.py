#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import os
import re
import sys
import time
import textwrap
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from colorama import init, Fore, Style
import openai
import requests
import random  # 导入random模块用于随机选择API密钥
from dotenv import load_dotenv
import httpx
import math
import traceback
from dotenv import load_dotenv

from RuleProcessor import RuleProcessor
from ScoreManager import ScoreManager

# 加载环境变量
env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
load_dotenv(dotenv_path=env_path, override=True)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log", encoding='utf-8', mode='w'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# 全局API配置
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
# 从环境变量中读取默认模型名称
DEFAULT_MODEL = os.getenv("MODEL_NAME", "gemini-2.0-flash-exp")


class LLMInterface:
    """大模型接口：负责与大模型API交互"""

    def __init__(self, model_name=None, api_key=None, base_url=None, debug_log_file="api_debug.log"):
        """
        初始化大模型接口

        Args:
            model_name (str): 模型名称
            api_key (str): API密钥
            base_url (str): API基础URL
            debug_log_file (str): 调试日志文件路径
        """
        # 使用提供的模型名称或环境变量中的默认模型
        self.model_name = model_name if model_name else DEFAULT_MODEL
        self.debug_log_file = debug_log_file

        # 清空API调试日志文件
        try:
            # 以写模式打开文件（这会清空文件内容）
            with open(self.debug_log_file, 'w', encoding='utf-8') as f:
                f.write("")  # 写入空字符串，确保文件被清空
            logger.info(f"已清空API调试日志文件: {self.debug_log_file}")
        except Exception as e:
            logger.warning(f"清空API调试日志文件失败: {str(e)}")

        # 设置API密钥（优先级：参数 > 全局变量 > 环境变量）
        if api_key:
            self.api_keys = self._parse_api_keys(api_key)
        elif OPENAI_API_KEY:
            self.api_keys = self._parse_api_keys(OPENAI_API_KEY)
        else:
            env_api_key = os.getenv("OPENAI_API_KEY")
            if not env_api_key:
                raise ValueError("未提供API密钥，且环境变量中未设置OPENAI_API_KEY")
            self.api_keys = self._parse_api_keys(env_api_key)

        # 随机选择一个API密钥作为起始点，实现负载均衡
        if self.api_keys:
            self.current_key_index = random.randint(0, len(self.api_keys) - 1)
            logger.info(f"随机选择API密钥索引 {self.current_key_index} 作为起始点")
        else:
            self.current_key_index = 0

        # 设置API基础URL（优先级：参数 > 全局变量 > 默认值）
        if base_url:
            self.base_url = base_url
        elif OPENAI_BASE_URL:
            self.base_url = OPENAI_BASE_URL
        else:
            self.base_url = "https://api.openai.com/v1"

        # 配置OpenAI客户端 (使用当前API key)
        openai.api_key = self.get_current_api_key()
        openai.base_url = self.base_url

        # 获取API调用间隔时间（单位：秒）
        self.interval = int(os.getenv("INTERVAL", "0"))
        logger.info(f"设置API调用间隔：{self.interval}秒")

        # 初始化上次调用时间为0，确保第一次调用不需要等待
        self.last_api_call_time = 0

        # 存储大模型提取的文本范围结果缓存
        self.llm_extracted_texts = {}

    def _parse_api_keys(self, api_key_str: str) -> List[str]:
        """
        解析逗号分隔的多个API密钥

        Args:
            api_key_str (str): 逗号分隔的API密钥字符串

        Returns:
            List[str]: API密钥列表
        """
        # 分割并去除空格
        keys = [key.strip() for key in api_key_str.split(',')]
        # 过滤掉空字符串
        return [key for key in keys if key]

    def get_current_api_key(self) -> str:
        """
        获取当前API密钥

        Returns:
            str: 当前API密钥，如果没有API密钥则尝试从环境变量获取
        """
        if not self.api_keys:
            # 尝试从环境变量获取API密钥
            env_key = os.environ.get("OPENAI_API_KEY")
            if env_key:
                logger.info("使用环境变量中的API密钥")
                return env_key
            else:
                logger.error("没有可用的API密钥，也无法从环境变量获取")
                return "" # 返回空字符串，让后续调用API时失败，便于错误处理
        return self.api_keys[self.current_key_index]

    def rotate_to_next_api_key(self) -> str:
        """
        轮换到下一个API密钥

        Returns:
            str: 下一个API密钥，如果没有API密钥则返回None
        """
        if not self.api_keys:
            logger.warning("没有可用的API密钥，无法轮换")
            return None

        # 轮换到下一个索引
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)

        # 获取新的API密钥
        api_key = self.api_keys[self.current_key_index]

        logger.info(f"轮换到下一个API密钥 (索引: {self.current_key_index + 1}/{len(self.api_keys)})")

        return api_key

    def get_text_for_rule(self, rule: Dict) -> str:        """        根据规则路径智能提取对应的文本片段                Args:            rule (Dict): 规则字典                    Returns:            str: 规则对应的文本片段        """        # 从规则中提取路径        rule_path = rule.get("path", "")                # 找出规则所属的模块（通常是第一部分）        module_name = ""        if "." in rule_path:            module_name = rule_path.split(".")[0]        else:            module_name = rule_path                    logger.info(f"提取规则 {rule_path} 对应的文本片段，模块: {module_name}")                # 根据模块名称提取对应段落的文本        if module_name == "标题" or module_name.startswith("2.标题"):            # 标题规则应该只返回文章标题            logger.info(f"标题规则：返回文章标题")            return self.text_processor.title                # 处理首段/第一段规则        if module_name.startswith("首段") or module_name.startswith("第一段") or module_name.startswith("3.第一段"):            if self.text_processor and self.text_processor.paragraphs and len(self.text_processor.paragraphs) > 0:                logger.info(f"第一段规则：提取首段文本: {self.text_processor.paragraphs[0][:50]}...")                return self.text_processor.paragraphs[0]                # 处理其他段落规则 - 从模块名提取段落编号        para_match = re.search(r'第(\d+)段', module_name)        if para_match:            para_num = int(para_match.group(1))            para_index = para_num - 1  # 转为0-索引            if self.text_processor and self.text_processor.paragraphs and 0 <= para_index < len(self.text_processor.paragraphs):                logger.info(f"第{para_num}段规则：提取第{para_num}段文本: {self.text_processor.paragraphs[para_index][:50]}...")                return self.text_processor.paragraphs[para_index]                # 从规则路径中查找段落指示        parts = rule_path.split(".")        if len(parts) >= 2:            # 尝试从第一部分中提取段落编号            first_part = parts[0]            para_match_in_path = re.search(r'第(\d+)段', first_part)            if para_match_in_path:                para_num = int(para_match_in_path.group(1))                para_index = para_num - 1  # 转为0-索引                if self.text_processor and self.text_processor.paragraphs and 0 <= para_index < len(self.text_processor.paragraphs):                    logger.info(f"从路径'{rule_path}'提取段落编号: {para_num}")                    logger.info(f"提取第{para_num}段文本: {self.text_processor.paragraphs[para_index][:50]}...")                    return self.text_processor.paragraphs[para_index]                # 处理中文段落编号        chinese_para_match = re.search(r'第([一二三四五六七八九十])段', module_name)        if chinese_para_match:            chinese_num = chinese_para_match.group(1)            # 转换中文数字到阿拉伯数字            chinese_to_arabic = {"一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10}            if chinese_num in chinese_to_arabic:                para_num = chinese_to_arabic[chinese_num]                para_index = para_num - 1  # 转为0-索引                if self.text_processor and self.text_processor.paragraphs and 0 <= para_index < len(self.text_processor.paragraphs):                    logger.info(f"中文段落规则：提取第{para_num}段文本: {self.text_processor.paragraphs[para_index][:50]}...")                    return self.text_processor.paragraphs[para_index]                # 如果无法确定段落，使用规则的范围字段，并根据当前规则模块确定段落        if "rule" in rule and isinstance(rule["rule"], dict) and "范围" in rule["rule"]:            range_desc = rule["rule"]["范围"]            logger.info(f"使用规则的范围字段: {range_desc}")                        if self.text_processor:                # 设置当前规则模块以辅助文本提取                if hasattr(self.text_processor, 'set_current_rule_module'):                    self.text_processor.set_current_rule_module(module_name)                                    # 使用text_processor提取文本                extracted_text = self.text_processor.get_text_by_range(range_desc)                if extracted_text:                    logger.info(f"通过范围'{range_desc}'提取到文本: {extracted_text[:50]}...")                    return extracted_text                # 尝试从规则路径提取段落信息        for part in rule_path.split('.'):            # 尝试阿拉伯数字段落            para_match = re.search(r'第(\d+)段', part)            if para_match:                para_num = int(para_match.group(1))                para_index = para_num - 1  # 转为0-索引                if self.text_processor and self.text_processor.paragraphs and 0 <= para_index < len(self.text_processor.paragraphs):                    logger.info(f"最后尝试，从路径部分'{part}'提取段落编号: {para_num}")                    logger.info(f"提取第{para_num}段文本: {self.text_processor.paragraphs[para_index][:50]}...")                    return self.text_processor.paragraphs[para_index]                        # 尝试中文段落编号            chinese_para_match = re.search(r'第([一二三四五六七八九十])段', part)            if chinese_para_match:                chinese_num = chinese_para_match.group(1)                chinese_to_arabic = {"一": 1, "二": 2, "三": 3, "四": 4, "五": 5, "六": 6, "七": 7, "八": 8, "九": 9, "十": 10}                if chinese_num in chinese_to_arabic:                    para_num = chinese_to_arabic[chinese_num]                    para_index = para_num - 1  # 转为0-索引                    if self.text_processor and self.text_processor.paragraphs and 0 <= para_index < len(self.text_processor.paragraphs):                        logger.info(f"最后尝试，中文段落规则：提取第{para_num}段文本: {self.text_processor.paragraphs[para_index][:50]}...")                        return self.text_processor.paragraphs[para_index]                # 如果无法确定段落，则基于规则名称提供相应段落文本        module_match = None                # 尝试从模块名提取段落信息        if "第一段" in module_name or "首段" in module_name:            para_index = 0            if self.text_processor and self.text_processor.paragraphs and len(self.text_processor.paragraphs) > 0:                logger.info(f"默认提取第一段文本: {self.text_processor.paragraphs[0][:50]}...")                return self.text_processor.paragraphs[0]                # 如果都无法确定，返回警告并尝试最后的后备方案        logger.warning(f"无法从规则 {rule_path} 确定对应的段落，使用第一段作为默认")        if self.text_processor and self.text_processor.paragraphs and len(self.text_processor.paragraphs) > 0:            return self.text_processor.paragraphs[0]                # 最后的后备方案        logger.error(f"无法确定规则 {rule_path} 对应的文本片段，也找不到任何段落")        return ""

    def evaluate_text(self, title: str, rule: Dict, text_to_evaluate: str, max_retries=3, rule_index=0) -> Dict:
        """
        评估文本

        Args:
            title (str): 题目
            rule (Dict): 评分规则
            text_to_evaluate (str): 待评估的文本
            max_retries (int): 每个API密钥的最大重试次数
            rule_index (int): 规则序号

        Returns:
            Dict: 评估结果，包含分数和评语
        """
        # 记录原始文本，用于API调试
        logger.info(f"## 需要评分的文本片段")
        logger.info(f"{text_to_evaluate}")

        # 计算文本字数统计信息
        char_count_info = {}
        sentence_counts = []
        try:
            # 尝试导入TextProcessor类来进行字数统计
            from TextProcessor import TextProcessor
            temp_processor = TextProcessor("")

            # 统计整个文本的字符数
            char_count_info = temp_processor.count_characters(text_to_evaluate)
            logger.info(f"文本字数统计: {char_count_info}")

            # 统计每个句子的字符数
            sentence_counts = temp_processor.count_sentences_characters(text_to_evaluate)
            logger.info(f"句子字数统计: 共{len(sentence_counts)}个句子")
            for sent_info in sentence_counts:
                logger.info(f"  第{sent_info['index']}句: {sent_info['stats']['total']}字 - {sent_info['sentence'][:20]}...")
        except Exception as e:
            logger.error(f"字数统计失败: {str(e)}")
            # 使用简化版字数统计作为后备
            chinese_chars = re.findall(r'[\u4e00-\u9fa5]', text_to_evaluate)
            english_words = re.findall(r'[a-zA-Z0-9]+', text_to_evaluate)
            char_count_info = {
                "total": len(chinese_chars) + len(english_words),
                "chinese": len(chinese_chars),
                "english_words": len(english_words)
            }
            logger.info(f"使用简化版字数统计: {char_count_info}")

        # 记录所有API密钥的尝试次数，用于日志和调试
        all_keys_attempt_count = 0

        # 记录本次调用的失败次数
        failure_count = 0

        # 记录解析错误重试次数
        parse_error_retry_count = 0
        max_parse_error_retries = 5  # 最大解析错误重试次数

        # 检查规则范围是否为"全段"
        is_full_paragraph_rule = rule.get("范围", "") == "全段"

        # 获取API密钥总数，用于判断是否只有一个密钥
        total_api_keys = len(self.api_keys) if self.api_keys else 1
        single_api_key = (total_api_keys == 1)

        # 外部无限循环，持续尝试直到成功
        while True:
            all_keys_attempt_count += 1
            logger.info(f"开始第 {all_keys_attempt_count} 轮API密钥循环尝试")

            keys_tried_this_round = 0

            # 构建提示词 - 根据是否出现过解析错误来优化
            if parse_error_retry_count > 0:
                # 如果之前有解析错误，使用强化版提示词
                prompt = self._build_prompt(title, rule, text_to_evaluate, is_retry=True,
                                            retry_count=parse_error_retry_count,
                                            char_count_info=char_count_info,
                                            sentence_counts=sentence_counts)
                logger.info(f"使用强化版提示词（第{parse_error_retry_count}次重试）")
            else:
                # 首次尝试，使用标准提示词
                prompt = self._build_prompt(title, rule, text_to_evaluate,
                                           char_count_info=char_count_info,
                                           sentence_counts=sentence_counts)

            # 尝试使用每个API密钥
            while keys_tried_this_round < total_api_keys:
                keys_tried_this_round += 1
                current_key_index = self.current_key_index
                current_key = self.get_current_api_key()

                logger.info(f"使用API密钥 {current_key_index + 1}/{total_api_keys} 进行尝试")

                # 每个密钥尝试最多max_retries次，如果只有一个密钥则无限重试
                local_max_retries = max_retries
                if single_api_key:
                    # 当只有一个API密钥时，忽略max_retries参数，持续重试直到成功
                    local_max_retries = 1000000  # 设置一个非常大的值，实际上等于无限重试

                for attempt in range(local_max_retries):
                    try:
                        if self.model_name.startswith("gpt"):
                            response = self._call_openai(prompt)
                        else:
                            # 添加其他模型的支持
                            response = self._call_openai(prompt)  # 默认使用OpenAI

                        # 记录大模型返回的完整响应
                        logger.info(f"\n===== 大模型返回的完整响应 =====\n{response}\n===============================")

                        # 解析评估结果
                        result = self._parse_response(response, rule)

                        # 检查结果是否完整
                        # 对于"全段"范围的规则，不需要检查matching_sentence
                        is_incomplete = (result["score"] != 0 and
                                        (not result.get("comment") or result["comment"].strip() == ""))

                        # 如果不是全段规则，还需要检查matching_sentence
                        if not is_full_paragraph_rule:
                            is_incomplete = is_incomplete or ("matching_sentence" not in result or not result["matching_sentence"])

                        if is_incomplete:
                            failure_count += 1
                            logger.warning(f"API返回了不完整的结果：得分不为0但评语或匹配句子为空 (失败次数: {failure_count})")
                            print(f"[API警告] API返回了不完整的结果，将重试... (失败次数: {failure_count})", flush=True)
                            # 如果是最后一次尝试且非单API密钥模式，我们将接受不完整的结果，但会添加警告
                            if attempt == local_max_retries - 1 and not single_api_key:
                                if not result.get("comment") or result["comment"].strip() == "":
                                    result["comment"] = "API返回的评语为空"

                                # 只有在非"全段"规则且缺少matching_sentence时添加默认值
                                if not is_full_paragraph_rule and ("matching_sentence" not in result or not result["matching_sentence"]):
                                    result["matching_sentence"] = result.get("comment", "API返回的匹配句子为空")

                                logger.warning(f"已达到最大重试次数，接受不完整的结果并添加默认值 (总失败次数: {failure_count})")
                                print(f"[API警告] 已达到最大重试次数，接受不完整的结果 (总失败次数: {failure_count})", flush=True)
                            else:
                                # 抛出异常以触发重试
                                raise Exception("API返回了不完整的结果")

                        # 记录API调用信息和结果到调试日志文件
                        self._log_api_debug_info(prompt, response, result, rule, rule_index=rule_index)

                        # 成功返回结果
                        if failure_count > 0:
                            logger.info(f"成功获取评估结果，总共进行了 {all_keys_attempt_count} 轮API密钥循环，失败 {failure_count} 次")
                            print(f"[API成功] 成功获取评估结果 (尝试 {failure_count + 1} 次后成功)", flush=True)
                        else:
                            logger.info(f"成功获取评估结果，首次尝试即成功")
                            print(f"[API成功] 成功获取评估结果 (首次尝试成功)", flush=True)

                        # 检查是否存在解析错误，如果存在则重试
                        if result.get("parse_error", False):
                            parse_error_retry_count += 1
                            if parse_error_retry_count <= max_parse_error_retries:
                                logger.warning(f"检测到解析错误，正在进行第 {parse_error_retry_count}/{max_parse_error_retries} 次重试")
                                print(f"[解析错误] 检测到解析错误，正在进行第 {parse_error_retry_count}/{max_parse_error_retries} 次重试", flush=True)

                                # 等待一段时间后重试
                                wait_time = 2
                                print(f"[解析等待] 等待 {wait_time} 秒后重试...", flush=True)
                                time.sleep(wait_time)

                                # 不返回结果，继续尝试
                                continue
                            else:
                                # 超过最大重试次数，抛出异常
                                error_msg = f"连续 {max_parse_error_retries} 次解析错误，评分失败"
                                logger.error(error_msg)

                                # 构建错误消息，包含更详细的评分规则信息
                                rule_info = f"规则路径: {rule.get('path', '未知')}"
                                if 'rule' in rule:
                                    rule_info += f", 范围: {rule['rule'].get('范围', '未知')}"
                                    rule_info += f", 规则: {rule['rule'].get('规则', '未知')}"

                                print(f"[严重错误] {error_msg}：{rule_info}", flush=True)

                                # 抛出异常
                                raise Exception(f"{error_msg}：{rule_info}")

                        return result

                    except Exception as e:
                        failure_count += 1
                        logger.error(f"API密钥 {current_key_index + 1}/{total_api_keys} 调用失败（尝试 {attempt+1}/{local_max_retries if not single_api_key else '无限'}）: {str(e)} (失败次数: {failure_count})")

                        # 记录错误信息到调试日志
                        self._log_api_debug_info(prompt, str(e), {"score": 0, "comment": f"API调用失败: {str(e)}"}, rule, is_error=True, rule_index=rule_index)

                        # 最后一次尝试失败，如果不是单个API密钥模式，不再为当前密钥继续
                        if attempt == local_max_retries - 1 and not single_api_key:
                            logger.warning(f"API密钥 {current_key_index + 1}/{total_api_keys} 已达到最大重试次数，将切换到下一个密钥 (总失败次数: {failure_count})")
                            break

                        # 等待一段时间后重试
                        wait_time = 10 if single_api_key else 1  # 如果只有1个API密钥，等待10秒
                        print(f"[API等待] 等待 {wait_time} 秒后重试... (失败次数: {failure_count})", flush=True)
                        time.sleep(wait_time)

                # 当前密钥的所有尝试都失败，轮换到下一个密钥（如果有多个密钥）
                if not single_api_key:
                    self.rotate_to_next_api_key()
                    logger.info(f"切换到下一个API密钥，当前索引: {self.current_key_index + 1}/{total_api_keys}")

            # 如果只有一个API密钥，前面已经设置为无限重试，不会到达这里
            # 如果有多个密钥，所有密钥都尝试过一轮仍然失败，记录日志
            if not single_api_key:
                logger.warning(f"第 {all_keys_attempt_count} 轮API密钥循环尝试失败，所有密钥都已尝试。休息5秒后将重新开始。 (总失败次数: {failure_count})")

                # 为防止过快重试，等待稍长时间
                time.sleep(5)

            # 如果是测试环境，可以设置最大循环次数以避免无限循环
            if 'TESTING' in os.environ and all_keys_attempt_count >= int(os.environ.get('MAX_API_CYCLES', 3)):
                logger.error(f"达到测试环境设置的最大API循环次数: {all_keys_attempt_count} (总失败次数: {failure_count})")
                return {"score": 0, "comment": f"达到最大API尝试次数限制，评分失败 (失败 {failure_count} 次)"}

    def _log_api_debug_info(self, prompt: str, response: str, result: Dict, rule: Dict, is_error=False, rule_index=0):
        """
        记录API调用的调试信息

        Args:
            prompt (str): 发送给API的提示词
            response (str): API的原始响应
            result (Dict): 解析后的结果
            rule (Dict): 评分规则
            is_error (bool): 是否为错误信息
            rule_index (int): 规则序号
        """
        try:
            # 检查规则范围是否为"全段"
            is_full_paragraph_rule = rule.get("范围", "") == "全段"

            # 过滤响应中的<think>块
            filtered_response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL).strip()

            timestamp = time.strftime("%Y-%m-%d %H:%M:%S", time.localtime())

            # 提取规则名称和模块名称
            rule_path = rule.get('path', '未知')
            parts = rule_path.split('.')

            # 特殊处理文本范围提取任务
            task_type = None

            # 增加更多检测条件来识别文本范围提取任务
            is_text_extraction = False

            # 检查规则中是否直接标记为文本提取
            if rule and isinstance(rule, dict) and rule.get("is_text_extraction") == True:
                is_text_extraction = True

            # 检查提示词中是否包含文本范围提取关键词
            if prompt and ("文本范围提取" in prompt or "精确识别符合范围描述的文本片段" in prompt):
                is_text_extraction = True

            # 检查结果中是否标记为文本提取
            if result and isinstance(result, dict) and result.get("is_text_extraction") == True:
                is_text_extraction = True

            # 检查规则中是否包含范围描述
            if rule and isinstance(rule, dict) and "范围" in rule and rule.get("规则", "").startswith("精确提取"):
                is_text_extraction = True

            # 如果识别为文本范围提取任务，设置相应的任务类型和模块名
            if is_text_extraction:
                task_type = "文本范围提取"

                # 添加调试输出
                print(f"\n[DEBUG] 检测到文本范围提取任务", flush=True)
                if prompt and "文本范围提取" in prompt:
                    print(f"[DEBUG] 提示词包含'文本范围提取'", flush=True)
                if result and isinstance(result, dict) and result.get("is_text_extraction") == True:
                    print(f"[DEBUG] 结果标记为文本提取任务", flush=True)
                if rule and isinstance(rule, dict) and "范围" in rule:
                    print(f"[DEBUG] 规则包含范围描述: {rule.get('范围')}", flush=True)

                # 尝试从规则中提取范围描述
                range_desc = rule.get("范围", "未知范围") if isinstance(rule, dict) else "未知范围"

                # 从范围描述中提取模块和规则信息
                module_name = "复杂文本提取"  # 修改: 从"文本提取"改为"复杂文本提取"
                rule_name = range_desc

                # 添加调试输出
                print(f"[DEBUG] 设置 module_name='{module_name}', rule_name='{rule_name}'", flush=True)

                # 如果范围描述中包含规则引用，设置更具体的规则名称
                if isinstance(range_desc, str):
                    rule_match = re.search(r'规则(\d+)的句子', range_desc)
                    if rule_match:
                        rule_num = rule_match.group(1)
                        rule_name = f"规则{rule_num}的句子提取"
                        print(f"[DEBUG] 检测到规则引用，更新 rule_name='{rule_name}'", flush=True)
            else:
                # 提取规则名称 - 通常是path中最后一部分
                rule_name = parts[-1] if len(parts) > 0 else rule_path

                # 提取模块名称 - 通常是path中第一部分
                module_name = parts[0] if len(parts) > 0 else "未知模块"

                print(f"[DEBUG] 非文本范围提取任务，设置 module_name='{module_name}', rule_name='{rule_name}'", flush=True)

            # 如果是错误信息，只输出到命令行，不写入debug日志文件
            if is_error:
                logger.error(f"API调用错误 ({timestamp}):")
                logger.error(f"模型: {self.model_name}, API密钥索引: {self.current_key_index + 1}")
                logger.error(f"错误响应: {response}")
                logger.error(f"解析结果: {json.dumps(result, ensure_ascii=False)}")
                print(f"\n[API错误] {timestamp} - 评估失败: {result.get('error', '未知错误')}", flush=True)
                return

            # 输出到控制台
            if task_type == "文本范围提取":
                print(f"\n[API结果] {timestamp} - {module_name}：{rule_name}", flush=True)
                if 'comment' in result:
                    comment = result['comment']
                    if comment == "" or comment.strip() == "":
                        print(f"[API结果] 警告: 评语为空", flush=True)
                    # 如果评语过长，截断显示
                    elif len(comment) > 100:
                        print(f"[API结果] 评语: {comment[:100]}...", flush=True)
                    else:
                        print(f"[API结果] 评语: {comment}", flush=True)
                else:
                    print(f"[API结果] 警告: 结果中没有评语字段", flush=True)

                # 只有在非"全段"规则时添加匹配句子的输出
                if not is_full_paragraph_rule:
                    if 'matching_sentence' in result:
                        matching_sentence = result['matching_sentence']

                        # 修改：检查matching_sentence是否为列表
                        if isinstance(matching_sentence, list):
                            # 如果是列表，将其转换为字符串显示
                            print(f"[DEBUG] matching_sentence是列表: {matching_sentence}", flush=True)

                            # 列表为空的情况
                            if not matching_sentence:
                                print(f"[API结果] 警告: 匹配句子列表为空", flush=True)
                            else:
                                # 如果列表内容过长，截断显示
                                display_text = str(matching_sentence)
                                if len(display_text) > 100:
                                    print(f"[API结果] 匹配句子: {display_text[:100]}...", flush=True)
                                else:
                                    print(f"[API结果] 匹配句子: {display_text}", flush=True)
                        else:
                            # 原有的字符串处理逻辑
                            if matching_sentence == "" or (hasattr(matching_sentence, 'strip') and matching_sentence.strip() == ""):
                                print(f"[API结果] 警告: 匹配句子为空", flush=True)
                            # 如果匹配句子过长，截断显示
                            elif hasattr(matching_sentence, '__len__') and len(matching_sentence) > 100:
                                print(f"[API结果] 匹配句子: {matching_sentence[:100]}...", flush=True)
                            else:
                                print(f"[API结果] 匹配句子: {matching_sentence}", flush=True)
                    else:
                        print(f"[API结果] 警告: 结果中没有匹配句子字段", flush=True)
            else:
                print(f"\n[API结果] {timestamp} - {module_name}规则{rule_index}：{rule_name}：{rule_path}", flush=True)
                print(f"[API结果] 得分: {result.get('score', 0)}", flush=True)
                if 'comment' in result:
                    comment = result['comment']
                    if comment == "" or comment.strip() == "":
                        print(f"[API结果] 警告: 评语为空", flush=True)
                    # 如果评语过长，截断显示
                    elif len(comment) > 100:
                        print(f"[API结果] 评语: {comment[:100]}...", flush=True)
                    else:
                        print(f"[API结果] 评语: {comment}", flush=True)
                else:
                    print(f"[API结果] 警告: 结果中没有评语字段", flush=True)

                # 只有在非"全段"规则时添加匹配句子的输出
                if not is_full_paragraph_rule:
                    if 'matching_sentence' in result:
                        matching_sentence = result['matching_sentence']

                        # 修改：检查matching_sentence是否为列表
                        if isinstance(matching_sentence, list):
                            # 如果是列表，将其转换为字符串显示
                            print(f"[DEBUG] matching_sentence是列表: {matching_sentence}", flush=True)

                            # 列表为空的情况
                            if not matching_sentence:
                                print(f"[API结果] 警告: 匹配句子列表为空", flush=True)
                            else:
                                # 如果列表内容过长，截断显示
                                display_text = str(matching_sentence)
                                if len(display_text) > 100:
                                    print(f"[API结果] 匹配句子: {display_text[:100]}...", flush=True)
                                else:
                                    print(f"[API结果] 匹配句子: {display_text}", flush=True)
                        else:
                            # 原有的字符串处理逻辑
                            if matching_sentence == "" or (hasattr(matching_sentence, 'strip') and matching_sentence.strip() == ""):
                                print(f"[API结果] 警告: 匹配句子为空", flush=True)
                            # 如果匹配句子过长，截断显示
                            elif hasattr(matching_sentence, '__len__') and len(matching_sentence) > 100:
                                print(f"[API结果] 匹配句子: {matching_sentence[:100]}...", flush=True)
                            else:
                                print(f"[API结果] 匹配句子: {matching_sentence}", flush=True)
                    else:
                        print(f"[API结果] 警告: 结果中没有匹配句子字段", flush=True)

            # 将调试信息写入文件
            with open(self.debug_log_file, "a", encoding="utf-8") as f:
                f.write(f"===== API调用日志 {timestamp} =====\n")
                f.write(f"模型: {self.model_name}\n")
                f.write(f"当前API密钥索引: {self.current_key_index + 1}\n")

                # 添加调试信息
                print(f"[DEBUG] 准备写入日志，task_type='{task_type}', module_name='{module_name}'", flush=True)

                # 创建要写入的日志行
                # 修改：针对文本范围提取任务，使用特定格式的日志头部
                if task_type == "文本范围提取":
                    log_header = f"{module_name}：{rule_name}: {json.dumps(rule, ensure_ascii=False)}"
                else:
                    log_header = f"{module_name}规则{rule_index}：{rule_name}: {json.dumps(rule, ensure_ascii=False)}"

                print(f"[DEBUG] 日志头部行：'{log_header}'", flush=True)

                if task_type == "文本范围提取":
                    # 修改: 使用与非文本范围提取任务相同的格式，但使用修改后的module_name("复杂文本提取")
                    f.write(f"{log_header}\n")
                    f.write(f"\n--- 提示词 ---\n{prompt}\n")
                    f.write(f"\n--- 响应 ---\n{filtered_response}\n")
                    f.write(f"\n--- 解析结果 ---\n{json.dumps(result, ensure_ascii=False)}\n")
                    f.write(f"===================================\n\n")

                    # 添加明确的日志，显示我们正在记录范围提取的提示词和响应
                    logger.info(f"已将文本范围提取的提示词和响应内容记录到 {self.debug_log_file} 文件中")
                    logger.info(f"范围描述: {rule.get('范围', '未知范围')}")
                    print(f"[API日志] 已记录文本范围提取 \"{rule.get('范围', '未知范围')}\" 的提示词和响应到 {self.debug_log_file}", flush=True)
                else:
                    f.write(f"{log_header}\n")
                    f.write(f"\n--- 提示词 ---\n{prompt}\n")
                    f.write(f"\n--- 响应 ---\n{filtered_response}\n")  # 使用过滤后的响应
                    f.write(f"\n--- 解析结果 ---\n{json.dumps(result, ensure_ascii=False)}\n")
                    f.write(f"===================================\n\n")
        except Exception as e:
            logger.error(f"记录API调试信息失败: {str(e)}")
            traceback.print_exc()

    def _build_prompt(self, content: str, rule: Dict, extraction_rule: Union[Dict, str] = None,
                     is_retry=False, retry_count=0, char_count_info: Dict = None,
                     sentence_counts: List[Dict] = None) -> str:
        """
        构建用于评估的提示词

        Args:
            content (str): 待评估的原始内容
            rule (Dict): 评分规则
            extraction_rule (Union[Dict, str], optional): 提取规则（用于文本范围提取任务）或文本内容
            is_retry (bool, optional): 是否为重试提示词
            retry_count (int, optional): 重试次数
            char_count_info (Dict, optional): 文本整体字数统计信息
            sentence_counts (List[Dict], optional): 每个句子的字数统计信息

        Returns:
            str: 构建好的提示词
        """
        # 初始化提示词
        prompt = ""

        # 检查extraction_rule参数类型
        # 如果是字符串，说明是旧版调用方式，将其视为text_to_evaluate
        if extraction_rule is not None and isinstance(extraction_rule, str):
            # 旧的调用方式：_build_prompt(title, rule, text_to_evaluate)
            # 在这种情况下，content实际上是title，extraction_rule是text_to_evaluate
            title = content
            text_to_evaluate = extraction_rule

            # 检查是否是文本范围提取任务
            is_text_extraction = (title == "文本范围提取" or
                                 (isinstance(rule, dict) and rule.get("规则", "").startswith("精确提取")))

            if is_text_extraction:
                # 文本范围提取任务的格式化输出
                range_desc = rule.get("范围", "未知范围")

                print("\n")
                print("=" * 80)
                print("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼ 文本范围提取开始 ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼")
                print(f"【范围描述】: {range_desc}")
                print("-" * 80)

                # 添加规则参考信息（如果有）
                rule_references = ""
                if "规则参考" in rule:
                    rule_references = "## 规则参考信息\n"
                    for rule_num, sentence in rule["规则参考"].items():
                        rule_references += f"规则{rule_num}的句子: {sentence}\n"
                    rule_references += "\n"

                # 构建文本范围提取的提示词
                # 从规则路径中提取段落信息
                current_module = ""
                paragraph_context = ""

                if "path" in rule:
                    rule_path = rule.get("path", "")
                    current_module = rule_path.split(".")[0] if "." in rule_path else rule_path

                    # 尝试从模块名中提取段落信息
                    para_match = re.search(r'第(\d+)段|第([一二三四五六七八九十]+)段', current_module)
                    if para_match:
                        para_num = para_match.group(1) or para_match.group(2)
                        paragraph_context = f"本次提取范围限定在第{para_num}段内。"
                    elif "首段" in current_module or "第一段" in current_module:
                        paragraph_context = "本次提取范围限定在第一段（首段）内。"
                    elif "尾段" in current_module or "最后一段" in current_module:
                        paragraph_context = "本次提取范围限定在最后一段（尾段）内。"

                # 检查是否包含"第X句或第Y句"格式的范围描述
                has_or_sentences = re.search(r'第[一二三四五六七八九十\d]+句或第[一二三四五六七八九十\d]+句', range_desc)

                # 如果范围描述包含"第X句"但不包含段落信息，且我们有段落上下文，添加说明
                has_sentence_no_paragraph = re.search(r'第[一二三四五六七八九十\d]+句', range_desc) and not re.search(r'段|首段|尾段', range_desc)

                # 构建段落上下文提示
                paragraph_hint = ""

                # 对于"第X句或第Y句"格式，添加特别说明
                if has_or_sentences:
                    logger.info(f"检测到'或'连接的句子范围描述: '{range_desc}'")

                    # 添加段落上下文
                    if paragraph_context:
                        paragraph_hint = f"\n\n## 段落上下文\n{paragraph_context}\n当范围描述中只说\"第几句\"而没有指定段落时，应在上述指定的段落中寻找对应的句子。\n另外，当范围描述中包含\"或\"字（如\"第二句或第三句\"）时，**需要同时提取所有指定的部分，而不是只选择其中一个**。"
                    else:
                        # 即使没有段落上下文，也要添加"或"的解释
                        paragraph_hint = f"\n\n## 特别说明\n当范围描述中包含\"或\"字（如\"第二句或第三句\"）时，**需要同时提取所有指定的部分，而不是只选择其中一个**。"

                # 处理常规的段落上下文提示
                elif has_sentence_no_paragraph and paragraph_context:
                    paragraph_hint = f"\n\n## 段落上下文\n{paragraph_context}\n当范围描述中只说\"第几句\"而没有指定段落时，应在上述指定的段落中寻找对应的句子。"

                prompt = f"""# 文本范围提取任务

你的任务是根据给定的范围描述，从提供的内容中准确提取对应的文本片段。请严格按照指定的范围进行提取，不要添加额外的解释或评论。

{rule_references}## 提取范围
"{range_desc}"{paragraph_hint}

## 内容
{text_to_evaluate}

## 句子分割规则
文本中的句子以句号（。）、感叹号（！）、问号（？）为分割标志。请按照以下方式识别句子：
- 第一句：从文本开头到第一个句号/感叹号/问号
- 第二句：第一个句号/感叹号/问号之后到第二个句号/感叹号/问号
- 第三句：第二个句号/感叹号/问号之后到第三个句号/感叹号/问号
- 以此类推...

## 输出要求
1. 仅输出提取的文本内容，不需要包含范围描述或其他说明
2. 如果范围描述不明确或找不到匹配的内容，请写明"未找到匹配内容"
3. 请严格按照文档中的原文提取，不要改变任何词语
4. 不要添加额外的解释、评论或分析
5. **重要说明**：当范围描述中包含"或"字（如"第二句或第三句"）时，这表示**需要同时提取所有指定的部分**（即第二句和第三句），而不是只选择其中一个。你需要提取并连接所有指定的句子。

## 处理示例
如果要求提取"第二句或第三句"，请：
1. 首先按句号识别所有句子
2. 找到第二句和第三句
3. 将两句连接输出（如：第二句内容第三句内容）

请现在开始提取：
"""

                # 将提示词打印到控制台以便调试
                print(f"[构建提示词] 文本范围提取提示词:\n{'-'*40}\n{prompt}\n{'-'*40}", flush=True)
                print("-" * 80)
                print("开始进行文本范围提取操作...")

                # 将完整提示词记录到应用日志中
                logger.info(f"\n===== 文本范围提取的提示词 =====\n{prompt}\n===============================")

                return prompt

            # 普通评分任务的原有逻辑
            # 创建评分规则的副本，添加分数信息
            rule_copy = rule.copy()

            # 获取规则路径和规则名称（如果存在）
            rule_name = rule.get("path", "").split(".")[-1] if "path" in rule else None

            # 如果规则中有"分额"字段，添加到提示词中
            max_score = rule.get("分额", 0)

            # 如果规则没有具体的评分标准，但有分值，添加一个默认的评分标准
            if max_score > 0 and "规则" not in rule:
                rule_copy["规则"] = f"根据文本内容进行评分，满分{max_score}分"

            # 为以下类型的范围描述提供更精确的说明
            if "范围" in rule_copy:
                # 记录原始范围描述，便于调试
                original_range = rule_copy["范围"]

                # 针对复杂的范围描述（包含规则引用或特定句子位置），提供更清晰的说明
                if re.search(r'规则\d+的句子|之后的一句|第[一二三四五六七八九十\d]+句', original_range):
                    logger.info(f"简化复杂范围描述：从 '{original_range}' 改为 '下面列出的需要评分的文本片段'")
                    rule_copy["范围"] = "下面列出的需要评分的文本片段"
                # 针对特定段落或位置的范围描述，保留更精确的信息
                elif original_range in ["首段", "首段任意位置", "第一段", "全段", "整段"]:
                    logger.info(f"保留精确的范围描述：'{original_range}'")
                elif original_range in ["首段最后一句", "首段末句"]:
                    logger.info(f"保留精确的范围描述：'{original_range}'")
                elif re.match(r'^第\d+段$', original_range) or re.match(r'^第[一二三四五六七八九十]+段$', original_range):
                    logger.info(f"保留精确的范围描述：'{original_range}'")
            # 如果评分规则中没有范围字段，添加默认范围
            elif "范围" not in rule_copy:
                rule_copy["范围"] = "全文"

            # 确保规则中有"最高得分"字段
            if max_score > 0 and "最高得分" not in rule_copy:
                rule_copy["最高得分"] = max_score

            # 如果有规则名称，添加到规则信息中
            if rule_name:
                rule_copy["规则名称"] = rule_name

            # 添加字数统计信息
            char_count_section = ""
            if char_count_info:
                char_count_section = f"""
## 文本字数统计
- 总字数（不含标点符号）: {char_count_info.get('total', 0)}
- 中文字符数: {char_count_info.get('chinese', 0)}
- 英文单词数: {char_count_info.get('english_words', 0)}
- 标点符号数: {char_count_info.get('punctuation', 0) if 'punctuation' in char_count_info else '未统计'}
- 空格数: {char_count_info.get('spaces', 0) if 'spaces' in char_count_info else '未统计'}
"""

            # 添加句子字数统计信息
            if sentence_counts and len(sentence_counts) > 0:
                char_count_section += "\n## 句子字数统计\n"
                for sent_info in sentence_counts:
                    sent_index = sent_info.get('index', 0)
                    sent_stats = sent_info.get('stats', {})
                    sent_text = sent_info.get('sentence', '')

                    # 如果句子太长，只显示前30个字符
                    if len(sent_text) > 30:
                        sent_preview = sent_text[:30] + "..."
                    else:
                        sent_preview = sent_text

                    char_count_section += f"- 第{sent_index}句: {sent_stats.get('total', 0)}字 - {sent_preview}\n"

                char_count_section += "\n**注意**: 在评估任何与字数相关的规则时，请使用上述统计数据，而不是自行计算。\n"
            elif char_count_info:
                char_count_section += "\n**注意**: 在评估任何与字数相关的规则时，请使用上述统计数据，而不是自行计算。\n"

            prompt = f"""你是一位中国公务员考试的申论部分考官，你的任务是根据评分标准，为考生作文的特定部分评分。

## 题目
{title}

## 评分标准
```json
{json.dumps(rule_copy, ensure_ascii=False, indent=2)}
```

## 需要评分的文本片段
{text_to_evaluate}
{char_count_section}
## 评分要求
重要：下面规则中"句"，是指需要评分的文本片段中以句号或分号为单位分隔的完整句子。如果提供评分的文本片段中只有一句话，则直接针对这句话进行评分，不要把句子拆开。
1. 先输出你的详细思考过程
2. 严格按照评分标准,逐句进行评分，再进行总结找出最符合标准的句子
3. 给出具体的得分（整数分值，如1分、2分等）"""

            # 如果是重试情况，添加强化版格式要求
            if is_retry:
                prompt += f"""

【注意】这是第{retry_count}次尝试，之前返回的格式可能不正确导致解析错误。
请严格按照要求，确保最终输出的JSON格式完整且可解析。不要使用单引号，使用双引号包围字符串，确保JSON格式正确。
"""

            # 如果规则中有"分额"字段，提示模型最高可给的分数
            if max_score > 0:
                prompt += f"\n4. 最高得分为{max_score}分"
                prompt += "\n5. 解释得分理由，说明满足或不满足标准的具体内容"

                # 对于范围为"全段"的规则，不要求返回matching_sentence
                is_full_paragraph_rule = rule.get("范围", "") == "全段"
                if is_full_paragraph_rule:
                    logger.info(f"范围为'全段'的规则，不要求返回matching_sentence: {rule.get('path', '未知路径')}")
                    prompt += """\n6. 最后用JSON格式输出结果，包含以下字段：
   - score: 分数值（整数）
   - comment: 简短的评语，说明得分理由

   example:
   {
    "score": 1,
    "comment": "得分理由"
   }

   注意：不要转义JSON字段中的引号和特殊字符，保持原始文本格式。
   """
                else:
                    prompt += """\n6. 最后用JSON格式输出结果，包含以下字段：
   - score: 分数值（整数）
   - comment: 简短的评语，说明得分理由
   - matching_sentence: 如果文本中有明确满足评分标准的句子，提供最符合标准的完整的一句
   - sentence_position: 匹配句子在段落中的位置，如"第2句"或"第一句"（请使用阿拉伯数字或中文数字表示）

   example:
   {
    "score": 1,
    "comment": "得分理由",
    "matching_sentence": "完整句子",
    "sentence_position": "第2句"
   }

   注意：不要转义JSON字段中的引号和特殊字符，保持原始文本格式。如文本中带有引号，直接保留即可，不要添加反斜杠。
   """
            else:
                prompt += "\n4. 解释得分理由，说明满足或不满足标准的具体内容"

                # 对于范围为"全段"的规则，不要求返回matching_sentence
                is_full_paragraph_rule = rule.get("范围", "") == "全段"
                if is_full_paragraph_rule:
                    logger.info(f"范围为'全段'的规则，不要求返回matching_sentence: {rule.get('path', '未知路径')}")
                    prompt += """\n5. 最后用JSON格式输出结果，包含以下字段：
   - score: 分数值（整数）
   - comment: 简短的评语，说明得分理由

   example:
   {
    "score": 1,
    "comment": "得分理由"
   }

   注意：不要转义JSON字段中的引号和特殊字符，保持原始文本格式。
   """
                else:
                    prompt += """\n5. 最后用JSON格式输出结果，包含以下字段：
   - score: 分数值（整数）
   - comment: 简短的评语，说明得分理由
   - matching_sentence: 如果文本中有明确满足评分标准的句子，提供最符合标准的完整的一句
   - sentence_position: 匹配句子在段落中的位置，如"第2句"或"第一句"（请使用阿拉伯数字或中文数字表示）

   example:
   {
    "score": 1,
    "comment": "得分理由",
    "matching_sentence": "完整句子",
    "sentence_position": "第2句"
   }

   注意：不要转义JSON字段中的引号和特殊字符，保持原始文本格式。如文本中带有引号，直接保留即可，不要添加反斜杠。
   """

            # 如果是重试情况，添加更严格的JSON格式要求
            if is_retry:
                prompt += f"""

最终JSON输出必须严格按照以下格式，确保双引号完整，不要使用单引号，不要转义文本中的引号和特殊字符：
```json
{{
  "score": 0,  // 整数分值
  "comment": "评语内容",  // 字符串，使用双引号
  "matching_sentence": "匹配的句子，如果有引号不需要转义"  // 字符串，使用双引号
}}
```

注意：
1. 不要转义JSON字段中的引号和特殊字符，使用原始引号即可
2. 如matching_sentence中包含"引号"，直接输出为"匹配的"引号"句子"，不需要转义为"匹配的\\"引号\\"句子"
3. 文本中的标点符号保持原样，不要添加额外的反斜杠
"""

            # 检查是否需要找出匹配句子 - 只有范围不是"全段"的情况下才添加识别提示
            is_full_paragraph_rule = rule.get("范围", "") == "全段"
            if not is_full_paragraph_rule:
                sentences = []
                for para in text_to_evaluate.split('\n'):
                    if para.strip():
                        sentences.extend(re.split(r'[。！？]|\.(?:\s|$)', para))
                sentences = [s.strip() for s in sentences if s.strip()]

            prompt += "\n请先输出详细的思考过程，然后再输出JSON格式的评分结果。"

            # 添加关于不转义特殊字符的特别说明
            prompt += """
特别注意：在生成JSON时，不要转义文本中的引号或其他特殊字符。比如，
正确示例：{"matching_sentence": "这是带"引号"的句子"}
错误示例：{"matching_sentence": "这是带\\"引号\\"的句子"}
"""

            # 在评分要求后插入特别要求
            prompt += """

特别要求：
- "matching_sentence"字段必须只包含一整句，不能包含多个句子。句子的边界以"。！？."等标点为准，不能跨越多个句子。
- 如果有多个句子都符合要求，只输出最符合评分标准的那一句，不要合并多句。
- matching_sentence 示例（正确）："'力'是指行政执法工作中的公信力。"
- matching_sentence 示例（错误）："'力'是指行政执法工作中的公信力。'理'是指在热法过程中要讲清事理、法理、情理。"
"""

            # 将完整提示词记录到应用日志中
            logger.info(f"\n===== 发送给大模型的完整提示词 =====\n{prompt}\n===============================")

            return prompt

        # 新的调用方式：_build_prompt(content, rule, extraction_rule)
        # 根据任务类型构建不同的提示词
        if extraction_rule is not None and isinstance(extraction_rule, dict):
            module_name = rule.get("模块", "未知模块")
            rule_name = rule.get("规则", "未知规则")
            range_description = extraction_rule.get("范围", "未知范围")

            is_multi_range = False

            # 检查是否为多范围的情况（并列关系，用分号、顿号或逗号分隔）
            parallel_separators = [";", "；", "、"]
            sequential_separators = [",", "，"]
            all_separators = parallel_separators + sequential_separators

            # 检查所有可能的分隔符
            for separator in all_separators:
                if separator in range_description:
                    # 发现多范围描述
                    is_multi_range = True
                    print(f"[构建提示词] 检测到多范围描述: '{range_description}'，使用分隔符: '{separator}'", flush=True)
                    break

            # 分隔符直接确定是并列还是递进关系
            is_parallel = any(sep in range_description for sep in parallel_separators)
            is_sequential = not is_parallel and any(sep in range_description for sep in sequential_separators)

            # 为多范围构建专门的提示词
            if is_multi_range:
                print("\n")
                print("=" * 80)
                print("■■■■■■■■■■■■■■■■ 构建提取提示词开始 ■■■■■■■■■■■■■■■■")
                print(f"【范围描述】: {range_description}")
                print("-" * 80)

                # 并列关系（多范围各自独立）
                if is_parallel:
                    ranges = []
                    for separator in parallel_separators:
                        if separator in range_description:
                            parts = range_description.split(separator)
                            ranges.extend([part.strip() for part in parts if part.strip()])

                    if not ranges:  # 如果没有正确分割，则使用整个描述
                        ranges = [range_description]

                    # 添加关于规则的参考信息
                    rule_references = ""
                    if "规则参考" in extraction_rule:
                        rule_references = "## 规则参考信息\n"
                        for rule_num, sentence in extraction_rule["规则参考"].items():
                            rule_references += f"规则{rule_num}的句子: {sentence}\n"
                        rule_references += "\n"

                    # 从规则路径中提取段落信息
                    current_module = ""
                    paragraph_context = ""

                    if "path" in rule:
                        rule_path = rule.get("path", "")
                        current_module = rule_path.split(".")[0] if "." in rule_path else rule_path

                        # 尝试从模块名中提取段落信息
                        para_match = re.search(r'第(\d+)段|第([一二三四五六七八九十]+)段', current_module)
                        if para_match:
                            para_num = para_match.group(1) or para_match.group(2)
                            paragraph_context = f"本次提取范围限定在第{para_num}段内。"
                        elif "首段" in current_module or "第一段" in current_module:
                            paragraph_context = "本次提取范围限定在第一段（首段）内。"
                        elif "尾段" in current_module or "最后一段" in current_module:
                            paragraph_context = "本次提取范围限定在最后一段（尾段）内。"

                    # 检查是否需要添加段落上下文
                    has_sentence_no_paragraph = False
                    for r in ranges:
                        if re.search(r'第[一二三四五六七八九十\d]+句', r) and not re.search(r'段|首段|尾段', r):
                            has_sentence_no_paragraph = True
                            break

                    # 构建段落上下文提示
                    paragraph_hint = ""
                    if has_sentence_no_paragraph and paragraph_context:
                        paragraph_hint = f"\n\n## 段落上下文\n{paragraph_context}\n当范围描述中只说\"第几句\"而没有指定段落时，应在上述指定的段落中寻找对应的句子。"

                    # 构建并列多范围提示词
                    prompt = f"""# 文本范围提取任务

你的任务是根据给定的范围描述，从提供的内容中准确提取对应的文本片段。请严格按照指定的范围进行提取，不要添加额外的解释或评论。

{rule_references}## 提取范围
以下是需要提取的多个文本范围，请分别提取每个范围的内容：
{', '.join([f'"{r}"' for r in ranges])}{paragraph_hint}

## 内容
{content}

## 句子分割规则
文本中的句子以句号（。）、感叹号（！）、问号（？）为分割标志。请按照以下方式识别句子：
- 第一句：从文本开头到第一个句号/感叹号/问号
- 第二句：第一个句号/感叹号/问号之后到第二个句号/感叹号/问号
- 第三句：第二个句号/感叹号/问号之后到第三个句号/感叹号/问号
- 以此类推...

## 输出要求
1. 对于每个范围，请按照以下格式提供提取结果：
   范围描述：提取的文本内容
2. 如果某个范围描述不明确或找不到匹配的内容，请写明"未找到匹配内容"
3. 请严格按照文档中的原文提取，不要改变任何词语
4. 不要添加额外的解释、评论或分析
5. **重要说明**：当单个范围描述中包含"或"字（如"第二句或第三句"）时，这表示**需要同时提取所有指定的部分**（即第二句和第三句），而不是只选择其中一个。你需要提取并连接所有指定的句子。

## 处理示例
如果某个范围要求提取"第二句或第三句"，请：
1. 首先按句号识别所有句子
2. 找到第二句和第三句
3. 将两句连接输出（如：第二句内容第三句内容）

请现在开始提取：
"""
                # 递进关系（后一个范围基于前一个范围的结果）
                elif is_sequential:
                    ranges = []
                    for separator in sequential_separators:
                        if separator in range_description:
                            parts = range_description.split(separator)
                            ranges.extend([part.strip() for part in parts if part.strip()])

                    if not ranges:  # 如果没有正确分割，则使用整个描述
                        ranges = [range_description]

                    # 添加关于规则的参考信息
                    rule_references = ""
                    if "规则参考" in extraction_rule:
                        rule_references = "## 规则参考信息\n"
                        for rule_num, sentence in extraction_rule["规则参考"].items():
                            rule_references += f"规则{rule_num}的句子: {sentence}\n"
                        rule_references += "\n"

                    # 从规则路径中提取段落信息
                    current_module = ""
                    paragraph_context = ""

                    if "path" in rule:
                        rule_path = rule.get("path", "")
                        current_module = rule_path.split(".")[0] if "." in rule_path else rule_path

                        # 尝试从模块名中提取段落信息
                        para_match = re.search(r'第(\d+)段|第([一二三四五六七八九十]+)段', current_module)
                        if para_match:
                            para_num = para_match.group(1) or para_match.group(2)
                            paragraph_context = f"本次提取范围限定在第{para_num}段内。"
                        elif "首段" in current_module or "第一段" in current_module:
                            paragraph_context = "本次提取范围限定在第一段（首段）内。"
                        elif "尾段" in current_module or "最后一段" in current_module:
                            paragraph_context = "本次提取范围限定在最后一段（尾段）内。"

                    # 检查是否需要添加段落上下文
                    has_sentence_no_paragraph = False
                    for r in ranges:
                        if re.search(r'第[一二三四五六七八九十\d]+句', r) and not re.search(r'段|首段|尾段', r):
                            has_sentence_no_paragraph = True
                            break

                    # 构建段落上下文提示
                    paragraph_hint = ""
                    if has_sentence_no_paragraph and paragraph_context:
                        paragraph_hint = f"\n\n## 段落上下文\n{paragraph_context}\n当范围描述中只说\"第几句\"而没有指定段落时，应在上述指定的段落中寻找对应的句子。"

                    # 构建递进多范围提示词
                    prompt = f"""# 文本范围提取任务

你的任务是根据给定的范围描述，从提供的内容中准确提取对应的文本片段。这是一个递进式提取任务，后一个范围的提取基于前一个范围的结果。

{rule_references}## 提取范围
以下是需要依次提取的范围描述（每一步基于前一步的结果）：
{', '.join([f'"{r}"' for r in ranges])}{paragraph_hint}

## 内容
{content}

## 句子分割规则
文本中的句子以句号（。）、感叹号（！）、问号（？）为分割标志。请按照以下方式识别句子：
- 第一句：从文本开头到第一个句号/感叹号/问号
- 第二句：第一个句号/感叹号/问号之后到第二个句号/感叹号/问号
- 第三句：第二个句号/感叹号/问号之后到第三个句号/感叹号/问号
- 以此类推...

## 提取步骤说明
1. 首先根据第一个范围描述 "{ranges[0]}" 从原始内容中提取文本
2. 然后根据第二个范围描述 "{ranges[1] if len(ranges) > 1 else '无'}" 从第一步提取的结果中继续提取
3. 依此类推，直到完成所有范围的提取

## 输出要求
1. 仅输出最终提取的文本内容，不需要中间步骤
2. 如果任何步骤无法找到匹配的内容，请写明"未找到匹配内容"
3. 请严格按照文档中的原文提取，不要改变任何词语
4. 不要添加额外的解释、评论或分析
5. **重要说明**：在任何一个提取步骤中，当范围描述包含"或"字（如"第二句或第三句"）时，这表示**需要同时提取所有指定的部分**（即第二句和第三句），而不是只选择其中一个。你需要提取并连接所有指定的句子。

## 处理示例
如果某个提取步骤要求提取"第二句或第三句"，请：
1. 首先按句号识别所有句子
2. 找到第二句和第三句
3. 将两句连接输出（如：第二句内容第三句内容）

请现在开始提取：
"""
            else:
                # 单一范围提取的提示词
                print("\n")
                print("=" * 80)
                print("■■■■■■■■■■■■■■■■ 构建提取提示词开始 ■■■■■■■■■■■■■■■■")
                print(f"【范围描述】: {range_description}")
                print("-" * 80)

                # 添加关于规则的参考信息
                rule_references = ""
                if "规则参考" in extraction_rule:
                    rule_references = "## 规则参考信息\n"
                    for rule_num, sentence in extraction_rule["规则参考"].items():
                        rule_references += f"规则{rule_num}的句子: {sentence}\n"
                    rule_references += "\n"

                # 从规则路径中提取段落信息
                current_module = ""
                paragraph_context = ""

                if "path" in rule:
                    rule_path = rule.get("path", "")
                    current_module = rule_path.split(".")[0] if "." in rule_path else rule_path

                    # 尝试从模块名中提取段落信息
                    para_match = re.search(r'第(\d+)段|第([一二三四五六七八九十]+)段', current_module)
                    if para_match:
                        para_num = para_match.group(1) or para_match.group(2)
                        paragraph_context = f"本次提取范围限定在第{para_num}段内。"
                    elif "首段" in current_module or "第一段" in current_module:
                        paragraph_context = "本次提取范围限定在第一段（首段）内。"
                    elif "尾段" in current_module or "最后一段" in current_module:
                        paragraph_context = "本次提取范围限定在最后一段（尾段）内。"

                # 如果范围描述包含"第X句"但不包含段落信息，且我们有段落上下文，添加说明
                has_sentence_no_paragraph = re.search(r'第[一二三四五六七八九十\d]+句', range_description) and not re.search(r'段|首段|尾段', range_description)

                # 构建段落上下文提示
                paragraph_hint = ""
                if has_sentence_no_paragraph and paragraph_context:
                    paragraph_hint = f"\n\n## 段落上下文\n{paragraph_context}\n当范围描述中只说\"第几句\"而没有指定段落时，应在上述指定的段落中寻找对应的句子。"

                # 检查是否需要先拆分段落为句子
                needs_sentence_split = re.search(r'第[一二三四五六七八九十\d]+句|句子', range_description)

                if needs_sentence_split:
                    # 将段落按句号、感叹号、问号拆分为句子
                    sentences = re.split(r'[。！？]', content)
                    sentences = [s.strip() for s in sentences if s.strip()]

                    # 构建句子列表格式的内容
                    sentence_list = ""
                    for i, sentence in enumerate(sentences, 1):
                        sentence_list += f"第{i}句：{sentence}\n"

                    prompt = f"""# 文本范围提取任务

你的任务是根据给定的范围描述，从提供的句子列表中准确提取对应的文本片段。请严格按照指定的范围进行提取，只输出需要的内容，不要有其他多余内容。

{rule_references}## 提取范围
"{range_description}"{paragraph_hint}

## 句子列表
{sentence_list}

## 输出要求
1. 仅输出提取的文本内容，不需要包含范围描述、句子编号或其他说明
2. 如果范围描述不明确或找不到匹配的内容，请写明"未找到匹配内容"
3. 请严格按照文档中的原文提取，不要改变任何词语
4. 不要添加额外的解释、评论或分析
5. **重要说明**：当范围描述中包含"或"字（如"第二句或第三句"）时，需要同时提取所有指定的部分并连接输出
6. 直接输出提取的文本内容，不要包含"第X句："这样的前缀
"""
                else:
                    prompt = f"""# 文本范围提取任务

你的任务是根据给定的范围描述，从提供的内容中准确提取对应的文本片段。请严格按照指定的范围进行提取，只输出需要的内容，不要有其他多余内容。

{rule_references}## 提取范围
"{range_description}"{paragraph_hint}

## 内容
{content}

## 输出要求
1. 仅输出提取的文本内容，不需要包含范围描述或其他说明
2. 如果范围描述不明确或找不到匹配的内容，请写明"未找到匹配内容"
3. 请严格按照文档中的原文提取，不要改变任何词语
4. 不要添加额外的解释、评论或分析
"""

            # 将提示词打印到控制台以便调试
            print(f"[构建提示词] 文本范围提取提示词:\n{'-'*40}\n{prompt}\n{'-'*40}", flush=True)
            print("-" * 80)
            print(">> 即将调用LLM进行文本范围提取操作... <<")
            print("-" * 80)

            # 将完整提示词记录到应用日志中
            logger.info(f"\n===== 文本范围提取的提示词 =====\n{prompt}\n===============================")

            return prompt
        else:
            # 正常的评分任务
            module_name = rule.get("模块", "未知模块")
            rule_name = rule.get("规则", "未知规则")

            # 构建评分提示词（可按需定制）
            prompt = f"""# 评分任务

## 评分规则
- 模块: {module_name}
- 规则: {rule_name}
- 分数范围: {rule.get('分数', '0-1分')}
- 评分标准: {rule.get('评分标准', '无')}

## 内容
{content}

## 要求
请根据上述评分规则对内容进行评估，并给出适当的得分和详细评语。

## 输出格式
请按照以下JSON格式输出评分结果：
```json
{
  "score": 分数值(数字),
  "comment": "详细评语",
  "matching_sentence": "与规则匹配的句子或段落"
}
```
"""

            # 将提示词记录到应用日志中
            logger.info(f"\n===== 评分提示词 =====\n{prompt}\n=======================")

            return prompt

    def _call_openai(self, prompt: str) -> str:
        """
        调用OpenAI API获取响应

        Args:
            prompt (str): 提示词

        Returns:
            str: API返回的响应

        Raises:
            Exception: API调用失败时抛出异常
        """
        try:
            # 检查是否需要等待以满足API调用间隔要求
            if self.interval > 0 and self.last_api_call_time > 0:  # 只有当不是第一次调用时才等待
                # 直接使用配置的interval值
                wait_time = self.interval
                logger.info(f"等待API调用间隔：{wait_time}秒")

                # 简单倒计时输出
                print(f"\n[等待下一次API调用] {wait_time}秒", flush=True)

                # 逐秒倒计时
                for i in range(wait_time, 0, -1):
                    print(f"\r[等待下一次API调用] {i}秒... ", end="", flush=True)
                    time.sleep(1)

                # 完成倒计时
                print("\r[等待完成] 开始调用API...               ", flush=True)

            # 更新上次API调用时间
            self.last_api_call_time = time.time()

            logger.info(f"调用API - 模型: {self.model_name}, API密钥索引: {self.current_key_index + 1}")
            print(f"[API调用] 模型: {self.model_name}, 密钥索引: {self.current_key_index + 1}", flush=True)

            # 使用新版OpenAI API (>=1.0.0)
            # 设置更长的超时时间
            client = openai.OpenAI(
                api_key=self.get_current_api_key(),
                base_url=self.base_url,
                http_client=httpx.Client(
                    timeout=httpx.Timeout(60.0, connect=10.0),  # 设置更长的总超时和连接超时
                )
            )

            # 使用流式输出
            response_content = ""
            in_think_block = False  # 跟踪是否在<think>块内
            console_output = []  # 用于控制台显示

            try:
                stream = client.chat.completions.create(
                    model=self.model_name,
                    messages=[
                        {"role": "system", "content": "你是一位严格遵循评分标准的公务员考试申论评分专家。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,  # 较低的温度以获得更一致的结果
                    max_tokens=4000,
                    stream=True  # 启用流式输出
                )

                # 处理流式响应
                for chunk in stream:
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content

                        # 实时输出到控制台
                        print(content, end="", flush=True)
                        console_output.append(content)

                        # 检查<think>标记
                        if "<think>" in content:
                            in_think_block = True

                        # 检查</think>标记
                        if "</think>" in content:
                            in_think_block = False
                            # 不添加</think>标记本身到response_content
                            continue

                        # 只有不在<think>块内的内容才添加到最终响应
                        if not in_think_block:
                            response_content += content

                # 控制台输出换行
                print()

                return response_content
            except httpx.HTTPStatusError as e:
                # 捕获HTTP状态错误并提供更明确的错误消息
                if e.response.status_code == 500:
                    error_msg = f"服务器内部错误 (500): 自建节点可能存在问题。URL: {self.base_url}, 模型: {self.model_name}"
                    logger.error(error_msg)

                    # 输出更详细的诊断信息
                    print(Fore.RED + "===== 服务器内部错误 (500) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")
                    print(f"请求消息长度: {len(prompt)} 字符")
                    print(f"提示词前100字符: {prompt[:100]}...")

                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")

                    print(Fore.RED + "这可能是由于以下原因造成:")
                    print("1. 自建节点服务器负载过高")
                    print("2. 请求内容过长或包含模型无法处理的内容")
                    print("3. 自建节点与底层模型API连接问题")
                    print("4. 网络传输中断")
                    print(Style.RESET_ALL)

                    raise Exception(error_msg)
                elif e.response.status_code == 429:
                    error_msg = f"请求频率过高 (429): API请求限制或配额不足。URL: {self.base_url}, 模型: {self.model_name}"

                    # 输出更详细的诊断信息
                    print(Fore.RED + "===== 请求频率限制错误 (429) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")

                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")

                    print(Fore.RED + "这可能是由于以下原因造成:")
                    print("1. API密钥已达到请求配额限制")
                    print("2. 短时间内请求次数过多")
                    print("3. 自建节点设置了请求频率限制")
                    print(Style.RESET_ALL)

                    raise Exception(error_msg)

                elif e.response.status_code == 401 or e.response.status_code == 403:
                    error_msg = f"认证错误 ({e.response.status_code}): API密钥可能无效或没有权限。URL: {self.base_url}, 模型: {self.model_name}"

                    # 输出更详细的诊断信息
                    print(Fore.RED + f"===== 认证错误 ({e.response.status_code}) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")
                    print(f"API密钥索引: {self.current_key_index + 1}/{len(self.api_keys) if self.api_keys else 1}")

                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")

                    print(Fore.RED + "这可能是由于以下原因造成:")
                    print("1. API密钥无效或过期")
                    print("2. 当前API密钥没有使用此模型的权限")
                    print("3. 自建节点认证配置错误")
                    print(Style.RESET_ALL)

                    raise Exception(error_msg)
                else:
                    # 其他HTTP错误
                    error_msg = f"HTTP请求错误 ({e.response.status_code})"

                    print(Fore.RED + f"===== HTTP错误 ({e.response.status_code}) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")

                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")

                    print(Style.RESET_ALL)

                    raise Exception(f"{error_msg}: {e.response.text}")
            except httpx.ReadTimeout:
                raise Exception(f"API请求超时: 自建节点响应时间过长。URL: {self.base_url}, 模型: {self.model_name}")
            except httpx.ConnectTimeout:
                raise Exception(f"连接超时: 无法连接到API服务器。URL: {self.base_url}, 模型: {self.model_name}")
            except httpx.RequestError as e:
                raise Exception(f"网络请求错误: {str(e)}")
        except Exception as e:
            logger.error(f"OpenAI API调用失败 (API key索引 {self.current_key_index + 1}): {str(e)}")
            raise

    def _parse_response(self, response: str, rule: Dict = None) -> Dict:
        """
        解析API响应

        Args:
            response (str): API响应内容
            rule (Dict, optional): 评分规则，用于检查范围类型

        Returns:
            Dict: 解析后的评估结果，包含以下字段:
                - score: 分数值
                - comment: 评语
                - matching_sentence: 匹配的句子（非全段规则时）
                - sentence_position: 句子在段落中的位置，如"第2句"（如果大模型返回）
                - parse_error: 布尔值，表示是否存在解析错误（新增字段）
        """
        try:
            print("\n[解析响应] 开始解析大模型输出...", flush=True)

            # 检查规则范围是否为"全段"
            is_full_paragraph_rule = rule and rule.get("范围", "") == "全段"
            if is_full_paragraph_rule:
                logger.info("当前规则范围为'全段'，将不添加matching_sentence字段")

            # 1. 过滤<think>块
            filtered_response = re.sub(r'<think>.*?</think>', '', response, flags=re.DOTALL).strip()

            # 打印过滤结果
            think_blocks_count = len(re.findall(r'<think>.*?</think>', response, re.DOTALL))
            if think_blocks_count > 0:
                print(f"[解析响应] 已过滤 {think_blocks_count} 个思考块", flush=True)

            # 保存原始响应用于调试
            logger.debug(f"原始响应:\n{response}")
            logger.debug(f"过滤后响应:\n{filtered_response}")

            # 2. 尝试多种方式提取JSON
            result = None

            # 2.1 首先尝试提取markdown格式的JSON代码块
            print("[解析响应] 尝试提取markdown格式的JSON代码块...", flush=True)
            json_matches = re.finditer(r'```(?:json)?\s*({\s*".*?"\s*:.*?})\s*```', filtered_response, re.DOTALL)
            json_matches_list = list(json_matches)  # 将迭代器转换为列表以便计数

            if json_matches_list:
                print(f"[解析响应] 找到 {len(json_matches_list)} 个可能的markdown JSON块", flush=True)

                # 重新创建迭代器
                json_matches = re.finditer(r'```(?:json)?\s*({\s*".*?"\s*:.*?})\s*```', filtered_response, re.DOTALL)
                for i, match in enumerate(json_matches):
                    try:
                        json_text = match.group(1)
                        print(f"[解析响应] 尝试解析第 {i+1} 个JSON块: {json_text[:50]}...", flush=True)

                        # 预处理JSON文本，修复一些常见问题
                        processed_json_text = self._preprocess_json_text(json_text)

                        candidate = json.loads(processed_json_text)
                        if isinstance(candidate, dict) and "score" in candidate:
                            result = candidate
                            print(f"[解析响应] 成功从markdown代码块提取JSON结果", flush=True)
                            break
                        else:
                            print(f"[解析响应] JSON格式不符合要求(缺少score字段)", flush=True)
                    except json.JSONDecodeError as e:
                        print(f"[解析响应] JSON解析错误: {e}", flush=True)
                        continue
            else:
                print("[解析响应] 未找到markdown格式的JSON代码块", flush=True)

            # 2.2 如果上面失败，尝试直接查找最外层花括号包围的内容
            if not result:
                print("[解析响应] 尝试提取裸JSON对象...", flush=True)
                json_candidates = []
                # 查找所有可能的JSON对象(简单花括号匹配)
                open_count = 0
                start_pos = None

                for i, char in enumerate(filtered_response):
                    if char == '{':
                        if open_count == 0:
                            start_pos = i
                        open_count += 1
                    elif char == '}':
                        open_count -= 1
                        if open_count == 0 and start_pos is not None:
                            json_candidates.append(filtered_response[start_pos:i+1])

                # 尝试解析找到的所有候选JSON
                if json_candidates:
                    print(f"[解析响应] 找到 {len(json_candidates)} 个可能的JSON对象", flush=True)
                    for i, json_str in enumerate(json_candidates):
                        try:
                            print(f"[解析响应] 尝试解析第 {i+1} 个JSON对象: {json_str[:50]}...", flush=True)

                            # 预处理JSON文本，修复一些常见问题
                            processed_json_str = self._preprocess_json_text(json_str)

                            candidate = json.loads(processed_json_str)
                            if isinstance(candidate, dict) and "score" in candidate:
                                result = candidate
                                print(f"[解析响应] 成功从裸JSON对象提取结果", flush=True)
                                break
                            else:
                                print(f"[解析响应] JSON格式不符合要求(缺少score字段)", flush=True)
                        except json.JSONDecodeError as e:
                            print(f"[解析响应] JSON解析错误: {e}", flush=True)
                            continue
                else:
                    print("[解析响应] 未找到有效的JSON对象", flush=True)

            # 3. 验证结果格式
            if result and isinstance(result, dict):
                required_fields = {"score", "comment"}
                if all(field in result for field in required_fields):
                    # 确保score是数字类型
                    try:
                        result["score"] = float(result["score"])
                        print(f"[解析响应] 成功解析结果: 得分={result['score']}, 评语=\"{result['comment'][:30]}...\"", flush=True)

                        # 检查是否包含句子位置信息
                        if "sentence_position" in result:
                            position_desc = result["sentence_position"]
                            print(f"[解析响应] 成功获取句子位置信息: \"{position_desc}\"", flush=True)
                            logger.info(f"大模型返回的句子位置: {position_desc}")
                        else:
                            print("[解析响应] 未包含句子位置信息", flush=True)
                            logger.info("大模型未返回句子位置信息")

                        # 处理字符串字段中的转义引号等符号
                        for field in ['comment', 'matching_sentence', 'sentence_position']:
                            if field in result and isinstance(result[field], str):
                                # 替换转义的引号和其他转义符号
                                result[field] = result[field].replace('\\"', '"').replace('\\\'', "'").replace('\\\\', '\\')
                                # 记录处理后的字段值
                                print(f"[解析响应] 处理转义符后的{field}: \"{result[field][:50]}...\"", flush=True)

                        # 检查关键字段是否为空
                        if result["score"] != 0 and (not result.get("comment") or result["comment"].strip() == ""):
                            print(f"[解析响应] 警告: API返回的评语为空，但得分不为0", flush=True)
                            logger.warning("API返回的评语为空，但得分不为0")
                            # 检查匹配句子是否为空（只有在非全段规则时检查）
                            if not is_full_paragraph_rule:
                                matching_sentence = result.get("matching_sentence")
                                is_empty_matching = False

                                # 检查不同类型的matching_sentence
                                if matching_sentence is None:
                                    is_empty_matching = True
                                elif isinstance(matching_sentence, list):
                                    is_empty_matching = not matching_sentence  # 空列表
                                elif hasattr(matching_sentence, 'strip'):
                                    is_empty_matching = not matching_sentence.strip()  # 空字符串
                                elif isinstance(matching_sentence, (dict, list, tuple)):
                                    is_empty_matching = not matching_sentence  # 空容器

                                if is_empty_matching:
                                    print(f"[解析响应] 警告: API返回的匹配句子也为空", flush=True)
                                    logger.warning("API返回的匹配句子为空")
                                    # 设置默认评语
                                    result["comment"] = "API返回的评语为空"

                        # 如果非全段规则且结果中没有matching_sentence字段，尝试从正文中提取作为匹配句子
                        if not is_full_paragraph_rule:
                            matching_sentence = result.get("matching_sentence")
                            need_add_matching = False

                            # 检查是否需要添加匹配句子
                            if matching_sentence is None:
                                need_add_matching = True
                            elif isinstance(matching_sentence, list):
                                need_add_matching = not matching_sentence  # 空列表
                            elif hasattr(matching_sentence, 'strip'):
                                need_add_matching = not matching_sentence.strip()  # 空字符串
                            elif isinstance(matching_sentence, (dict, tuple)):
                                need_add_matching = not matching_sentence  # 空容器

                            if need_add_matching:
                                # 添加较短的评语直接作为匹配句子
                                if len(result["comment"]) < 150:  # 限制短评语
                                    result["matching_sentence"] = result["comment"]
                                    print(f"[解析响应] 为结果添加匹配句子: \"{result['matching_sentence'][:30]}...\"", flush=True)
                                # 否则，从评语中尝试提取可能的匹配句子
                                else:
                                    # 尝试从评语中提取引用的文本（通常在引号内）
                                    quoted_text = re.search(r'["'"」](.*?)["'"」]', result["comment"])
                                    if quoted_text:
                                        result["matching_sentence"] = quoted_text.group(1)
                                        print(f"[解析响应] 从评语引用中提取匹配句子: \"{result['matching_sentence'][:30]}...\"", flush=True)

                        return result
                    except (TypeError, ValueError) as e:
                        error_msg = f"分数格式无效: {result['score']}"
                        print(f"[解析响应] 错误: {error_msg}", flush=True)
                        logger.error(error_msg)
                else:
                    missing_fields = required_fields - set(result.keys())
                    error_msg = f"缺少必要字段: {missing_fields}"
                    print(f"[解析响应] 错误: {error_msg}", flush=True)
                    logger.error(error_msg)

            # 4. 如果无法解析为JSON，检查是否是文本范围提取任务
            is_text_extraction = False
            is_parallel_ranges = False
            is_sequential_ranges = False

            # 检查是否是文本范围提取任务的多个条件
            if rule and isinstance(rule, dict):
                range_desc = rule.get("范围", "")

                # 检查是否包含并列范围描述（通过分号或顿号分隔）
                parallel_range_separators = [";", "；", "、"]
                for separator in parallel_range_separators:
                    if separator in range_desc:
                        is_parallel_ranges = True
                        break

                # 如果不是并列关系，检查是否是递进关系
                if not is_parallel_ranges:
                    sequential_range_separators = [",", "，"]
                    for separator in sequential_range_separators:
                        if separator in range_desc:
                            is_sequential_ranges = True
                            break

                if rule.get("规则", "").startswith("精确提取"):
                    is_text_extraction = True
                    print("[解析响应] 检测到规则开头为'精确提取'，判定为文本范围提取任务", flush=True)
                elif "范围" in rule and not is_full_paragraph_rule:
                    # 检查范围描述是否包含特定模式
                    if re.search(r'规则\d+的句子|第[一二三四五六七八九十\d]+句|之后的一句', range_desc):
                        is_text_extraction = True
                        print(f"[解析响应] 检测到特殊范围描述: '{range_desc}'，判定为文本范围提取任务", flush=True)

            # 检查响应内容
            if not is_text_extraction and (
                "文本范围提取" in filtered_response or
                not re.search(r'score|分数|评分|得分', filtered_response, re.IGNORECASE)
            ):
                is_text_extraction = True
                print("[解析响应] 根据响应内容判定为文本范围提取任务", flush=True)

            if is_text_extraction:
                # 将整个过滤后的响应作为提取的文本
                print("[解析响应] 检测到文本范围提取任务，将整个响应作为提取结果", flush=True)

                # 尝试清理响应中的非必要内容
                cleaned_response = filtered_response

                # 去除可能的markdown格式
                cleaned_response = re.sub(r'```.*?```', '', cleaned_response, flags=re.DOTALL).strip()

                # 去除常见的前导语，如"根据范围描述..."等
                common_prefixes = [
                    r'^根据范围描述.*?[:：]',
                    r'^符合范围描述的文本片段[:：]',
                    r'^提取的文本[:：]',
                    r'^文本片段[:：]',
                    r'^以下是.*?文本[:：]'
                ]
                for prefix in common_prefixes:
                    cleaned_response = re.sub(prefix, '', cleaned_response, flags=re.DOTALL).strip()

                # 如果是并列多范围提取，确保格式一致性
                if is_parallel_ranges:
                    # 对于并列多范围提取，保持原有格式（每行一个范围及其提取结果）
                    # 检查是否已经符合预期格式（范围：内容）
                    has_expected_format = False
                    lines = cleaned_response.split('\n')
                    for line in lines:
                        if re.search(r'[：:]\s*\S', line):  # 匹配包含冒号且冒号后有内容的行
                            has_expected_format = True
                            break

                    if not has_expected_format:
                        # 尝试根据原始范围描述构建格式化输出
                        if rule and isinstance(rule, dict):
                            range_desc = rule.get("范围", "")
                            ranges = []

                            # 只使用并列分隔符拆分
                            parallel_range_separators = [";", "；", "、"]
                            for separator in parallel_range_separators:
                                if separator in range_desc:
                                    parts = range_desc.split(separator)
                                    parts = [part.strip() for part in parts if part.strip()]
                                    ranges.extend(parts)

                            # 如果只有一个范围但响应包含多行，可能每行对应一个范围
                            if len(ranges) == 1 and len(lines) > 1:
                                cleaned_response = f"{ranges[0]}：{cleaned_response}"
                            elif len(ranges) > 1:
                                # 尝试匹配范围与响应行
                                formatted_lines = []
                                for i, r in enumerate(ranges):
                                    if i < len(lines):
                                        formatted_lines.append(f"{r}：{lines[i].strip()}")
                                    else:
                                        formatted_lines.append(f"{r}：未能提取")

                                cleaned_response = '\n'.join(formatted_lines)
                # 递进关系的范围描述（通过逗号分隔），保持单一结果格式
                elif is_sequential_ranges:
                    # 对于递进关系，结果应该是单一文本，不需要特殊格式
                    # 但需要确保结果是清晰的，去除可能的格式混乱
                    if rule and isinstance(rule, dict):
                        range_desc = rule.get("范围", "")
                        print(f"[解析响应] 处理递进关系范围描述的结果: '{range_desc}'", flush=True)

                        # 如果结果包含"："且看起来像是格式化输出，尝试提取真正的结果
                        if "：" in cleaned_response or ":" in cleaned_response:
                            # 尝试找到最后一个冒号并提取其后内容
                            last_colon_pos = max(cleaned_response.rfind("："), cleaned_response.rfind(":"))
                            if last_colon_pos >= 0:
                                real_content = cleaned_response[last_colon_pos+1:].strip()
                                if real_content:  # 确保提取的内容不为空
                                    cleaned_response = real_content
                                    print(f"[解析响应] 从递进关系结果中提取最终内容: '{cleaned_response[:50]}...'", flush=True)

                print(f"[解析响应] 清理后的文本提取结果: \"{cleaned_response[:100]}...\"", flush=True)

                # 添加文本范围提取的结束标记
                print("\n")
                print("=" * 80)
                print("▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼ LLM文本提取结果 ▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼▼")
                print("-" * 80)

                if rule and isinstance(rule, dict):
                    range_desc = rule.get("范围", "未知范围")
                    print(f"【范围描述】: {range_desc}")
                    print(f"【提取结果】: {cleaned_response}")
                else:
                    print(f"{cleaned_response}")

                print("-" * 80)
                print("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲ LLM文本提取结束 ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲")
                print("=" * 80)
                print("\n")

                # 对于文本提取结果，如果是全段规则，不添加matching_sentence字段
                result_dict = {
                    "score": 1,  # 使用统一的分数
                    "comment": cleaned_response,  # 将清理后的响应作为提取结果
                    "is_text_extraction": True  # 标记这是文本提取结果
                }

                return result_dict

            # 如果以上所有解析方式都失败，返回标记解析错误的结果
            error_msg = "无法解析响应内容为有效的评估结果"
            print(f"[解析响应] 错误: {error_msg}", flush=True)
            logger.error(error_msg)

            # 返回一个标记解析错误的结果
            return {
                "score": 0,
                "comment": "无法解析响应",
                "error": True,
                "parse_error": True  # 新增字段，标记为解析错误
            }
        except Exception as e:
            error_msg = f"解析响应时出错: {str(e)}"
            print(f"[解析响应] 异常: {error_msg}", flush=True)
            logger.error(error_msg)
            traceback.print_exc()

            # 返回一个标记解析错误的结果
            return {
                "score": 0,
                "comment": f"解析响应时出错: {str(e)}",
                "error": True,
                "parse_error": True  # 新增字段，标记为解析错误
            }

    def llm_extract_text_by_range(self, content: str, range_description: str) -> str:
        """
        使用LLM提取符合范围描述的文本

        Args:
            content (str): 原始文本内容
            range_description (str): 范围描述，例如"第一段第二句"

        Returns:
            str: 提取的文本
        """
        try:
            # 检查缓存
            cache_key = f"{hash(content)}_{range_description}"
            if cache_key in self.llm_extracted_texts:
                cached_result = self.llm_extracted_texts[cache_key]
                logger.info(f"[缓存命中] 范围描述: {range_description}, 内容: {cached_result[:50]}...")
                return cached_result

            # 创建规则字典
            extracted_rule = {
                "范围": range_description,
                "模块": "复杂文本提取",
                "规则": "精确提取文本范围",
                "is_text_extraction": True
            }

            # 检查是否包含规则引用（如"规则1的句子"）
            rule_references = {}

            # 检查是否有多个范围部分
            range_parts = [range_description]  # 默认为单一范围

            # 检查所有可能的分隔符
            separators = [";", "；", "、", ",", "，"]
            for separator in separators:
                if separator in range_description:
                    range_parts = [part.strip() for part in range_description.split(separator) if part.strip()]
                    break

            # 为每个范围部分检查规则引用
            for part in range_parts:
                rule_match = re.search(r'规则(\d+)的句子', part)
                if rule_match:
                    rule_num = rule_match.group(1)
                    # 获取对应规则的句子（需要在调用方传入）
                    # 在实际实现中，可能需要从文本处理器中获取
                    # 这里仅作为示例
                    pass

            # 构建提示词
            prompt = self._build_prompt(content, extracted_rule, extracted_rule)

            # 调用大模型API
            response = self.get_completion(prompt)

            # 解析响应
            result = self._parse_response(response, extracted_rule)

            # 从结果中提取文本内容
            extracted_text = result.get("comment", "")

            # 缓存结果
            self.llm_extracted_texts[cache_key] = extracted_text

            # 打印完成信息
            print("\n")
            print("=" * 80)
            print("▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲ 文本范围提取完成 ▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲▲")
            print("=" * 80)
            print("\n\n")

            return extracted_text

        except Exception as e:
            logger.exception(f"提取范围文本时出错: {str(e)}")
            print(f"[提取错误] {str(e)}", flush=True)
            return f"[提取错误] {str(e)}"

    def _preprocess_json_text(self, json_text: str) -> str:
        """
        预处理JSON文本，修复常见的格式问题

        Args:
            json_text (str): 原始JSON文本

        Returns:
            str: 处理后的JSON文本
        """
        # 存储原始文本用于比较
        original_text = json_text

        # 1. 替换中文引号为英文引号
        json_text = json_text.replace('"', '"').replace('"', '"')
        json_text = json_text.replace(''', "'").replace(''', "'")

        # 2. 替换不标准的注释
        json_text = re.sub(r'//.*?(\n|$)', '', json_text)  # 删除单行注释
        json_text = re.sub(r'/\*.*?\*/', '', json_text, flags=re.DOTALL)  # 删除多行注释

        # 3. 尝试直接解析
        try:
            json.loads(json_text)
            return json_text  # 如果能直接解析，直接返回
        except json.JSONDecodeError:
            # 继续处理
            pass

        # 4. 处理JSON字符串字段中的引号问题
        # 这个简单的方法可能并不总是有效，但对于大多数情况应该足够了
        pattern = r'"(\w+)"\s*:\s*"([^"\\]*(?:\\.[^"\\]*)*)"'

        def fix_field(match):
            field_name = match.group(1)
            value = match.group(2)

            # 检查值中是否有未转义的引号
            if '"' in value:
                # 转义所有引号
                new_value = value.replace('"', '\\"')
                logger.info(f"修复字段 {field_name} 中的引号: {value[:30]}... -> {new_value[:30]}...")
                return f'"{field_name}": "{new_value}"'
            return match.group(0)

        json_text = re.sub(pattern, fix_field, json_text)

        # 5. 处理尾部多余的逗号
        json_text = re.sub(r',\s*}', '}', json_text)

        # 6. 确保字段名有双引号
        json_text = re.sub(r'(\s*)(\w+)(\s*):([^:])', r'\1"\2"\3:\4', json_text)

        # 7. 最后的简单修复：针对明显的引号内引号问题
        if '"matching_sentence"' in json_text:
            # 找到matching_sentence字段及其值
            ms_match = re.search(r'"matching_sentence"\s*:\s*"(.*?)"(?=,|})', json_text, re.DOTALL)
            if ms_match:
                value = ms_match.group(1)
                if '"' in value:
                    # 简单替换所有引号
                    new_value = value.replace('"', '\\"')
                    old_str = f'"matching_sentence": "{value}"'
                    new_str = f'"matching_sentence": "{new_value}"'
                    json_text = json_text.replace(old_str, new_str)
                    logger.info(f"特别修复 matching_sentence 字段中的引号")

        # 输出处理前后的比较
        if original_text != json_text:
            logger.info(f"JSON文本已修复:\n原始: {original_text[:100]}...\n修复后: {json_text[:100]}...")
            print(f"[预处理JSON] 文本已修复", flush=True)

        return json_text

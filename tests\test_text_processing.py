"""
文本处理工具测试模块。
"""
import pytest
from typing import List, TYPE_CHECKING

from utils.text_processing import (
    chinese_to_arabic, 
    split_paragraphs, 
    split_sentences, 
    resolve_paragraph_reference,
    parse_sentence_position
)

if TYPE_CHECKING:
    from _pytest.capture import CaptureFixture
    from _pytest.fixtures import FixtureRequest
    from _pytest.logging import LogCaptureFixture
    from _pytest.monkeypatch import MonkeyPatch
    from pytest_mock.plugin import MockerFixture


class TestChineseToArabic:
    """测试中文数字转阿拉伯数字功能。"""
    
    @pytest.mark.parametrize("chinese_num, expected", [
        ("一", 1),
        ("二", 2),
        ("三", 3),
        ("十", 10),
        ("十一", 11),
        ("二十", 20),
        ("二十三", 23),
        ("123", 123),  # 测试数字字符串
        ("壹", 1),     # 测试繁体数字
        ("贰", 2),
    ])
    def test_valid_conversion(self, chinese_num: str, expected: int) -> None:
        """
        测试有效的中文数字转换。
        
        Args:
            chinese_num: 中文数字字符串
            expected: 预期的阿拉伯数字结果
        """
        assert chinese_to_arabic(chinese_num) == expected
    
    @pytest.mark.parametrize("invalid_input", [
        "abc",
        "一二三四",  # 不合法的中文数字格式
        "十二三",    # 不合法的中文数字格式
    ])
    def test_invalid_input(self, invalid_input: str) -> None:
        """
        测试无效输入的异常抛出。
        
        Args:
            invalid_input: 无效的中文数字输入
        """
        with pytest.raises(ValueError):
            chinese_to_arabic(invalid_input)


class TestSplitParagraphs:
    """测试文本分段功能。"""
    
    def test_empty_text(self) -> None:
        """测试空文本的处理。"""
        assert split_paragraphs("") == []
        assert split_paragraphs("  \n  \n  ") == []
    
    def test_single_paragraph(self) -> None:
        """测试单段文本的处理。"""
        text = "这是一个段落。"
        assert split_paragraphs(text) == [text]
    
    def test_multiple_paragraphs(self) -> None:
        """测试多段文本的处理。"""
        text = "第一段。\n\n第二段。\n\n第三段。"
        expected = ["第一段。", "第二段。", "第三段。"]
        assert split_paragraphs(text) == expected
    
    def test_mixed_separators(self) -> None:
        """测试混合分隔符的处理。"""
        text = "段落1\n\n段落2\r\n\r\n段落3"
        expected = ["段落1", "段落2", "段落3"]
        assert split_paragraphs(text) == expected
    
    def test_whitespace_handling(self) -> None:
        """测试空白字符的处理。"""
        text = "  段落1  \n\n  段落2  "
        expected = ["段落1", "段落2"]
        assert split_paragraphs(text) == expected


class TestSplitSentences:
    """测试句子分割功能。"""
    
    def test_empty_paragraph(self) -> None:
        """测试空段落的处理。"""
        assert split_sentences("") == []
        assert split_sentences("  ") == []
    
    def test_single_sentence(self) -> None:
        """测试单句段落的处理。"""
        paragraph = "这是一个句子。"
        assert split_sentences(paragraph) == [paragraph]
    
    def test_multiple_sentences(self) -> None:
        """测试多句段落的处理。"""
        paragraph = "句子1。句子2！句子3？"
        expected = ["句子1。", "句子2！", "句子3？"]
        assert split_sentences(paragraph) == expected
    
    def test_mixed_punctuation(self) -> None:
        """测试混合标点的处理。"""
        paragraph = "这是中文句子。This is English sentence. 混合句子！"
        expected = ["这是中文句子。", "This is English sentence. ", "混合句子！"]
        assert split_sentences(paragraph) == expected
    
    def test_quotes_handling(self) -> None:
        """测试引号的处理。"""
        paragraph = '他说："这很好。"然后离开了。'
        expected = ['他说："这很好。"', "然后离开了。"]
        assert split_sentences(paragraph) == expected


class TestResolveParagraphReference:
    """测试段落引用解析功能。"""
    
    @pytest.mark.parametrize("reference, total, expected", [
        ("首段", 5, 0),
        ("第一段", 5, 0),
        ("开头段落", 5, 0),
        ("第二段", 5, 1),
        ("第三段", 5, 2),
        ("尾段", 5, 4),
        ("最后一段", 5, 4),
        ("末段", 5, 4),
        ("结尾段落", 5, 4),
    ])
    def test_special_aliases(self, reference: str, total: int, expected: int) -> None:
        """
        测试特殊别名的解析。
        
        Args:
            reference: 段落引用描述
            total: 总段落数
            expected: 预期的段落索引
        """
        assert resolve_paragraph_reference(reference, total) == expected
    
    @pytest.mark.parametrize("reference, total, expected", [
        ("第一段", 5, 0),
        ("第二段", 5, 1),
        ("第三段", 5, 2),
        ("第十段", 15, 9),
    ])
    def test_numbered_references(self, reference: str, total: int, expected: int) -> None:
        """
        测试编号引用的解析。
        
        Args:
            reference: 段落引用描述
            total: 总段落数
            expected: 预期的段落索引
        """
        assert resolve_paragraph_reference(reference, total) == expected
    
    @pytest.mark.parametrize("invalid_reference", [
        "无效引用",
        "段落",
        "段落1",
    ])
    def test_invalid_references(self, invalid_reference: str) -> None:
        """
        测试无效引用的异常抛出。
        
        Args:
            invalid_reference: 无效的段落引用
        """
        with pytest.raises(ValueError):
            resolve_paragraph_reference(invalid_reference, 5)


class TestParseSentencePosition:
    """测试句子位置解析功能。"""
    
    @pytest.mark.parametrize("position_desc, current, expected", [
        ("第一句", 0, (0, 0)),
        ("第二句", 0, (0, 1)),
        ("第三句", 0, (0, 2)),
        ("第十句", 0, (0, 9)),
        ("第一句", 2, (2, 0)),  # 测试不同的当前段落
    ])
    def test_absolute_positions(self, position_desc: str, current: int, expected: tuple) -> None:
        """
        测试绝对位置的解析。
        
        Args:
            position_desc: 位置描述
            current: 当前段落索引
            expected: 预期的(段落索引,句子索引)元组
        """
        assert parse_sentence_position(position_desc, current) == expected
    
    @pytest.mark.parametrize("position_desc, current, expected", [
        ("该段第一句", 0, (0, 0)),
        ("本段第二句", 0, (0, 1)),
        ("该段第三句", 2, (2, 2)),
        ("本段第十句", 3, (3, 9)),
    ])
    def test_relative_positions(self, position_desc: str, current: int, expected: tuple) -> None:
        """
        测试相对位置的解析。
        
        Args:
            position_desc: 位置描述
            current: 当前段落索引
            expected: 预期的(段落索引,句子索引)元组
        """
        assert parse_sentence_position(position_desc, current) == expected
    
    @pytest.mark.parametrize("invalid_position", [
        "无效位置",
        "句子1",
        "段落第一句",
    ])
    def test_invalid_positions(self, invalid_position: str) -> None:
        """
        测试无效位置的异常抛出。
        
        Args:
            invalid_position: 无效的位置描述
        """
        with pytest.raises(ValueError):
            parse_sentence_position(invalid_position, 0)
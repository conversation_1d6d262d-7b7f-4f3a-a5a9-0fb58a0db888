#!/usr/bin/env python
# -*- coding: utf-8 -*-

import argparse
import json
import os
import re
import sys
import time
import textwrap
from typing import Dict, List, Tuple, Any, Optional, Union
import logging
import nltk
from nltk.tokenize import sent_tokenize, word_tokenize
from colorama import init, Fore, Style
import openai
import requests
import random  # 导入random模块用于随机选择API密钥
from dotenv import load_dotenv
import httpx
import math
import traceback
from dotenv import load_dotenv

from RuleProcessor import RuleProcessor 
from ScoreManager import ScoreManager

# 加载环境变量
env_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '.env')
load_dotenv(dotenv_path=env_path, override=True)

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("app.log", encoding='utf-8', mode='w'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


# 全局API配置
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY", "")
OPENAI_BASE_URL = os.getenv("OPENAI_BASE_URL", "https://api.openai.com/v1")
# 从环境变量中读取默认模型名称
DEFAULT_MODEL = os.getenv("MODEL_NAME", "gemini-2.0-flash-exp")

class TextProcessor:
    """文本处理器：用于段落和句子分割，以及文本片段提取"""
    
    def __init__(self, essay_text: str):
        """
        初始化文本处理器
        
        Args:
            essay_text (str): 学员作文文本
        """
        self.full_text = essay_text
        
        # 分离标题和正文
        lines = essay_text.split('\n')
        if lines:
            self.title = lines[0].strip()
            self.body_text = '\n'.join(lines[1:]).strip()
        else:
            self.title = ""
            self.body_text = essay_text
        
        # 使用换行符分段，每行作为一个段落
        self.paragraphs = self._split_paragraphs(self.body_text)
        self.sentences_by_para = [self._split_sentences(para) for para in self.paragraphs]
        
        # 构建映射字典，用于快速查找段落和句子
        self.paragraph_map = {
            "首段": 0,
            "第一段": 0,
            "第二段": 1 if len(self.paragraphs) > 1 else 0,
            "第三段": 2 if len(self.paragraphs) > 2 else 0,
            "第四段": 3 if len(self.paragraphs) > 3 else 0,
            "尾段": len(self.paragraphs) - 1,
            "最后一段": len(self.paragraphs) - 1,
        }
        
        # 规则评分历史，用于存储已评分的规则及其文本
        self.rule_text_map = {}
        # 存储特定规则的匹配句子
        self.rule_matching_sentences = {}
        # 存储大模型提取的范围文本
        self.llm_extracted_texts = {}
        
        # 记录日志
        logger.info(f"标题: '{self.title}'")
        logger.info(f"正文段落数: {len(self.paragraphs)}")
        for i, para in enumerate(self.paragraphs):
            sentences = self.sentences_by_para[i]
            logger.info(f"第{i+1}段包含 {len(sentences)} 个句子")
            for j, sent in enumerate(sentences):
                logger.info(f"第{i+1}段第{j+1}句: '{sent}'")
    
    def _split_paragraphs(self, text: str) -> List[str]:
        """
        将文本分割为段落，使用换行符作为分隔
        
        Args:
            text (str): 待分割的文本
            
        Returns:
            List[str]: 段落列表
        """
        # 直接按换行符分割
        lines = text.split('\n')
        # 过滤空行
        paragraphs = [line.strip() for line in lines if line.strip()]
        
        logger.info(f"按换行符分段，共得到 {len(paragraphs)} 个段落")
        return paragraphs
    
    def _split_sentences(self, paragraph: str) -> List[str]:
        """
        将段落分割为句子，支持中文和英文
        
        Args:
            paragraph (str): 待分割的段落
            
        Returns:
            List[str]: 句子列表
        """
        # 中文句子分割的正则表达式
        # 匹配中文句号、感叹号、问号，或英文句号后跟空格或行尾
        pattern = r'([。！？\.][\""\']?)'
        
        # 使用正则表达式切分句子
        parts = re.split(pattern, paragraph)
        
        # 重组句子（包含标点符号）
        sentences = []
        i = 0
        while i < len(parts) - 1:
            if i + 1 < len(parts):
                # 句子内容 + 标点符号
                sentences.append(parts[i] + parts[i+1])
                i += 2
            else:
                # 剩余的部分无标点
                if parts[i].strip():
                    sentences.append(parts[i])
                i += 1
        
        # 处理最后一个部分（如果有且没有结束标点）
        if i < len(parts) and parts[i].strip():
            sentences.append(parts[i])
        
        # 过滤空句子并去除首尾空白
        return [s.strip() for s in sentences if s.strip()]
    
    def get_text_by_range(self, range_desc: str) -> str:
        """
        根据范围描述获取对应的文本片段
        
        Args:
            range_desc (str): 范围描述，如"首段"、"第二段第一句"等
            
        Returns:
            str: 文本片段
        """
        # 检查范围描述是否为空
        if not range_desc:
            logger.warning("范围描述为空，无法获取文本")
            return ""
            
        # 预处理：去除可能的引号和首尾空格
        range_desc = range_desc.strip()
        if range_desc.startswith("'") and range_desc.endswith("'"):
            range_desc = range_desc[1:-1].strip()
        elif range_desc.startswith('"') and range_desc.endswith('"'):
            range_desc = range_desc[1:-1].strip()
        
        logger.info(f"处理预处理后的范围描述: '{range_desc}'")
        
        # 处理标题相关的范围描述
        if range_desc in ["标题", "整篇文章第一行", "标题部分", "整篇文章第一行的位置"]:
            logger.info(f"获取标题: '{self.title}'")
            return self.title
        
        # 优先处理简单的句子引用，如"第二句"或"第2句"
        chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
        
        # 匹配中文数字的句子引用，如"第二句"
        if re.match(r'^第[一二三四五六七八九十]+句$', range_desc):
            # 提取中文数字
            sent_match = re.search(r'第([一二三四五六七八九十]+)句', range_desc)
            chinese_num = sent_match.group(1)
            
            if chinese_num in chinese_num_map:
                sent_index = chinese_num_map[chinese_num] - 1  # 转为0-索引
                
                # 从模块名中提取段落索引
                current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
                para_index = 0  # 默认为首段
                
                if current_module:
                    # 先尝试阿拉伯数字
                    para_match = re.search(r'第(\d+)段', current_module)
                    if para_match:
                        para_num = int(para_match.group(1))
                        para_index = para_num - 1  # 转为0-索引
                        logger.info(f"中文数字句子引用 - 从模块名 {current_module} 中提取段落索引(阿拉伯数字): {para_index}")
                    else:
                        # 尝试中文数字
                        para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                        if para_match:
                            para_chinese_num = para_match.group(1)
                            if para_chinese_num in chinese_num_map:
                                para_num = chinese_num_map[para_chinese_num]
                                para_index = para_num - 1  # 转为0-索引
                                logger.info(f"中文数字句子引用 - 从模块名 {current_module} 中提取段落索引(中文数字): {para_index}")
                            else:
                                logger.warning(f"中文数字句子引用 - 无法转换中文数字: {para_chinese_num}，使用默认首段")
                        else:
                            logger.info(f"中文数字句子引用 - 无法从模块名 {current_module} 提取段落索引，使用默认首段")
                
                # 确保段落索引有效
                if 0 <= para_index < len(self.paragraphs):
                    # 获取该段落的所有句子
                    para_sentences = self.sentences_by_para[para_index] if para_index < len(self.sentences_by_para) else []
                    
                    # 确保句子索引有效
                    if 0 <= sent_index < len(para_sentences):
                        logger.info(f"中文数字句子引用 - 获取第{para_index+1}段第{sent_index+1}句: {para_sentences[sent_index][:30]}...")
                        return para_sentences[sent_index]
                    else:
                        logger.warning(f"中文数字句子引用 - 第{para_index+1}段没有第{sent_index+1}句")
                        return ""
                else:
                    logger.warning(f"中文数字句子引用 - 无法找到第{para_index+1}段，共有{len(self.paragraphs)}段")
                    return ""
        
        # 匹配阿拉伯数字的句子引用，如"第2句"
        if re.match(r'^第\d+句$', range_desc):
            # 提取句子索引
            sent_match = re.search(r'第(\d+)句', range_desc)
            sent_index = int(sent_match.group(1)) - 1  # 转为0-索引
            
            # 从模块名中提取段落索引
            current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
            para_index = 0  # 默认为首段
            
            if current_module:
                # 支持中文数字和阿拉伯数字
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                
                # 先尝试阿拉伯数字
                para_match = re.search(r'第(\d+)段', current_module)
                if para_match:
                    para_num = int(para_match.group(1))
                    para_index = para_num - 1  # 转为0-索引
                    logger.info(f"阿拉伯数字句子引用 - 从模块名 {current_module} 中提取段落索引(阿拉伯数字): {para_index}")
                else:
                    # 尝试中文数字
                    para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                    if para_match:
                        para_chinese_num = para_match.group(1)
                        if para_chinese_num in chinese_num_map:
                            para_num = chinese_num_map[para_chinese_num]
                            para_index = para_num - 1  # 转为0-索引
                            logger.info(f"阿拉伯数字句子引用 - 从模块名 {current_module} 中提取段落索引(中文数字): {para_index}")
                        else:
                            logger.warning(f"阿拉伯数字句子引用 - 无法转换中文数字: {para_chinese_num}，使用默认首段")
                    else:
                        logger.info(f"阿拉伯数字句子引用 - 无法从模块名 {current_module} 提取段落索引，使用默认首段")
            
            # 确保段落索引有效
            if 0 <= para_index < len(self.paragraphs):
                # 获取该段落的所有句子
                para_sentences = self.sentences_by_para[para_index] if para_index < len(self.sentences_by_para) else []
                
                # 确保句子索引有效
                if 0 <= sent_index < len(para_sentences):
                    logger.info(f"阿拉伯数字句子引用 - 获取第{para_index+1}段第{sent_index+1}句: {para_sentences[sent_index][:30]}...")
                    return para_sentences[sent_index]
                else:
                    logger.warning(f"阿拉伯数字句子引用 - 第{para_index+1}段没有第{sent_index+1}句")
                    return ""
            else:
                logger.warning(f"阿拉伯数字句子引用 - 无法找到第{para_index+1}段，共有{len(self.paragraphs)}段")
                return ""
        
        # 处理首段、任意位置等特定描述
        if range_desc in ["首段", "第一段", "首段任意位置"]:
            if len(self.paragraphs) > 0:
                logger.info(f"返回首段文本")
                return self.paragraphs[0]
                
        # 处理首段最后一句
        if range_desc in ["首段最后一句", "首段末句", "首段尾句"]:
            if len(self.paragraphs) > 0 and len(self.sentences_by_para[0]) > 0:
                logger.info(f"返回首段最后一句")
                return self.sentences_by_para[0][-1]
                
        # 处理第X段特定描述
        para_match = re.search(r'^第(\d+)段', range_desc)
        if para_match:
            para_num = int(para_match.group(1))
            para_index = para_num - 1  # 转为0-索引
            if 0 <= para_index < len(self.paragraphs):
                logger.info(f"返回第{para_num}段文本")
                return self.paragraphs[para_index]
                
        # 处理特定的中文段落名称
        if range_desc == "尾段" or range_desc == "最后一段":
            if len(self.paragraphs) > 0:
                logger.info(f"返回尾段文本")
                return self.paragraphs[-1]
                
        # 处理全文范围 - 修改为根据当前模块返回相应段落，而不是整篇文章
        if range_desc in ["全文", "整篇文章", "全段", "整段"]:
            # 获取当前模块名称
            current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
            
            # 如果有当前模块，尝试从模块名中提取段落索引
            if current_module:
                # 支持中文数字和阿拉伯数字
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                
                # 检查是否是标题模块
                if current_module == "标题":
                    logger.info(f"全文范围 - 当前模块是标题，返回标题文本")
                    return self.title or ""
                
                # 先尝试阿拉伯数字
                para_match = re.search(r'第(\d+)段', current_module)
                if para_match:
                    para_num = int(para_match.group(1))
                    para_index = para_num - 1  # 转为0-索引
                    if 0 <= para_index < len(self.paragraphs):
                        logger.info(f"全文范围 - 从模块名 {current_module} 提取段落索引: {para_index}，返回该段落")
                        return self.paragraphs[para_index]
                
                # 尝试中文数字
                para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                if para_match:
                    chinese_num = para_match.group(1)
                    if chinese_num in chinese_num_map:
                        para_num = chinese_num_map[chinese_num]
                        para_index = para_num - 1  # 转为0-索引
                        if 0 <= para_index < len(self.paragraphs):
                            logger.info(f"全文范围 - 从模块名 {current_module} 提取段落索引: {para_index}，返回该段落")
                            return self.paragraphs[para_index]
                
                # 检查是否是首段或第一段
                if "首段" in current_module or "第一段" in current_module:
                    if len(self.paragraphs) > 0:
                        logger.info(f"全文范围 - 当前模块是首段，返回第一段")
                        return self.paragraphs[0]
                
                # 检查是否是尾段或最后一段
                if "尾段" in current_module or "最后一段" in current_module:
                    if len(self.paragraphs) > 0:
                        logger.info(f"全文范围 - 当前模块是尾段，返回最后一段")
                        return self.paragraphs[-1]
            
            # 如果无法从模块名确定段落，记录警告并尝试其他方法确定段落
            logger.warning(f"全文范围 - 无法从模块名 {current_module} 直接确定段落，尝试匹配段落")
            
            # 最后的后备方案：如果还是无法确定，返回当前规则路径对应的段落
            # 这是基于规则路径通常包含段落信息的假设
            if current_module and "." in current_module:
                module_part = current_module.split(".")[0]  # 例如从"第一段.规则1"中提取"第一段"
                
                # 检查是否包含段落信息
                para_match = re.search(r'第(\d+)段', module_part)
                if para_match:
                    para_num = int(para_match.group(1))
                    para_index = para_num - 1  # 转为0-索引
                    if 0 <= para_index < len(self.paragraphs):
                        logger.info(f"全文范围 - 从规则路径 {module_part} 提取段落索引: {para_index}，返回该段落")
                        return self.paragraphs[para_index]
                
                # 尝试中文数字
                para_match = re.search(r'第([一二三四五六七八九十]+)段', module_part)
                if para_match:
                    chinese_num = para_match.group(1)
                    if chinese_num in chinese_num_map:
                        para_num = chinese_num_map[chinese_num]
                        para_index = para_num - 1  # 转为0-索引
                        if 0 <= para_index < len(self.paragraphs):
                            logger.info(f"全文范围 - 从规则路径 {module_part} 提取段落索引: {para_index}，返回该段落")
                            return self.paragraphs[para_index]
            
            # 仅在所有尝试都失败的情况下返回全文
            logger.warning(f"全文范围 - 所有匹配尝试都失败，返回全文")
            return self.full_text or ""
        
        # 处理整段范围 - 已在上面的全文范围处理中合并
        if False:  # 这个条件不会被执行，保留代码仅作历史参考
            # 默认处理当前模块对应的段落
            current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
            para_index = 0  # 默认为首段
            
            if current_module:
                # 支持中文数字和阿拉伯数字
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                
                # 先尝试阿拉伯数字
                para_match = re.search(r'第(\d+)段', current_module)
                if para_match:
                    para_num = int(para_match.group(1))
                    para_index = para_num - 1  # 转为0-索引
                    logger.info(f"整段范围 - 从模块名 {current_module} 中提取段落索引(阿拉伯数字): {para_index}")
                else:
                    # 尝试中文数字
                    para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                    if para_match:
                        chinese_num = para_match.group(1)
                        if chinese_num in chinese_num_map:
                            para_num = chinese_num_map[chinese_num]
                            para_index = para_num - 1  # 转为0-索引
                            logger.info(f"整段范围 - 从模块名 {current_module} 中提取段落索引(中文数字): {para_index}")
                        else:
                            logger.warning(f"整段范围 - 无法转换中文数字: {chinese_num}，使用默认首段")
                    else:
                        logger.info(f"整段范围 - 无法从模块名 {current_module} 提取段落索引，使用默认首段")
            
            # 确保索引有效
            if 0 <= para_index < len(self.paragraphs):
                logger.info(f"整段范围 - 返回第{para_index+1}段: {self.paragraphs[para_index][:30]}...")
                return self.paragraphs[para_index]
            
            logger.warning(f"整段范围 - 索引{para_index}无效，使用默认首段")
            return self.paragraphs[0] if self.paragraphs else ""
        
        # 处理直接的"第X句"、"第一句"、"首句"格式 - 特殊处理不依赖模块名
        direct_sentence_match = re.search(r'^(第(\d+)句|第一句|首句)$', range_desc)
        if direct_sentence_match:
            sent_index = 0  # 默认第一句
            
            if direct_sentence_match.group(2):  # 如果是"第X句"
                sent_index = int(direct_sentence_match.group(2)) - 1  # 转为0-索引
                
            # 从模块名中提取段落索引
            current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
            para_index = 0  # 默认为首段
            
            if current_module:
                # 支持中文数字和阿拉伯数字
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                
                # 先尝试阿拉伯数字
                para_match = re.search(r'第(\d+)段', current_module)
                if para_match:
                    para_num = int(para_match.group(1))
                    para_index = para_num - 1  # 转为0-索引
                    logger.info(f"直接句子引用 - 从模块名 {current_module} 中提取段落索引(阿拉伯数字): {para_index}")
                else:
                    # 尝试中文数字
                    para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                    if para_match:
                        para_chinese_num = para_match.group(1)
                        if para_chinese_num in chinese_num_map:
                            para_num = chinese_num_map[para_chinese_num]
                            para_index = para_num - 1  # 转为0-索引
                            logger.info(f"直接句子引用 - 从模块名 {current_module} 中提取段落索引(中文数字): {para_index}")
                        else:
                            logger.warning(f"直接句子引用 - 无法转换中文数字: {para_chinese_num}，使用默认首段")
                    else:
                        logger.info(f"直接句子引用 - 无法从模块名 {current_module} 提取段落索引，使用默认首段")
            
            # 确保段落索引有效
            if 0 <= para_index < len(self.paragraphs):
                # 获取该段落的所有句子
                para_sentences = self.sentences_by_para[para_index] if para_index < len(self.sentences_by_para) else []
                
                # 确保句子索引有效
                if 0 <= sent_index < len(para_sentences):
                    logger.info(f"直接句子引用 - 获取第{para_index+1}段第{sent_index+1}句: {para_sentences[sent_index][:30]}...")
                    return para_sentences[sent_index]
                else:
                    logger.warning(f"直接句子引用 - 第{para_index+1}段没有第{sent_index+1}句，共有 {len(para_sentences)} 句")
                    return ""
            else:
                logger.warning(f"直接句子引用 - 无法找到第{para_index+1}段，共有{len(self.paragraphs)}段")
                return ""
    
        # 处理首段最后一句话 或 第N段最后一句话
        last_sentence_match = re.search(r'(首段|第(\d+)段)最后一句话', range_desc)
        if last_sentence_match:
            # 确定段落索引
            if last_sentence_match.group(1) == "首段":
                para_index = 0  # 首段就是第一段(索引0)
            else:
                para_index = int(last_sentence_match.group(2)) - 1  # 转为0-based索引
            
            # 确保段落索引有效
            if 0 <= para_index < len(self.paragraphs):
                # 获取该段落的所有句子
                para_sentences = self.sentences_by_para[para_index] if para_index < len(self.sentences_by_para) else []
                
                # 获取最后一句的索引
                if para_sentences:
                    last_sent_index = len(para_sentences) - 1
                    logger.info(f"最后一句话 - 获取第{para_index+1}段的最后一句(第{last_sent_index+1}句): {para_sentences[last_sent_index][:30]}...")
                    return para_sentences[last_sent_index]
                else:
                    logger.warning(f"最后一句话 - 第{para_index+1}段没有句子")
                    return ""
            else:
                logger.warning(f"最后一句话 - 无法找到第{para_index+1}段，共有{len(self.paragraphs)}段")
                return ""
        
        # 处理段落引用，如"首段"、"第二段"等
        paragraph_match = re.match(r'^(首段|第[一二三四五六七八九十\d]+段|尾段|最后一段)$', range_desc)
        if paragraph_match:
            para_desc = paragraph_match.group(1)
            para_index = None
            
            # 映射段落描述到索引
            if para_desc == "首段":
                para_index = 0
            elif para_desc == "尾段" or para_desc == "最后一段":
                para_index = len(self.paragraphs) - 1
            else:
                # 处理"第X段"格式
                num_match = re.search(r'第([一二三四五六七八九十\d]+)段', para_desc)
                if num_match:
                    num_str = num_match.group(1)
                    
                    # 尝试解析为数字
                    try:
                        # 如果是数字字符串
                        para_num = int(num_str)
                        para_index = para_num - 1  # 转为0-索引
                    except ValueError:
                        # 如果是中文数字
                        if num_str in chinese_num_map:
                            para_num = chinese_num_map[num_str]
                            para_index = para_num - 1  # 转为0-索引
                        else:
                            logger.warning(f"无法解析段落数字: {num_str}")
                            para_index = 0  # 默认首段
            
            # 确保段落索引有效
            if para_index is not None and 0 <= para_index < len(self.paragraphs):
                paragraph_text = self.paragraphs[para_index]
                logger.info(f"获取{para_desc}: {paragraph_text[:30]}...")
                return paragraph_text
            else:
                logger.warning(f"无法找到{para_desc}，索引 {para_index}，共有{len(self.paragraphs)}段")
                return ""
                
        # 如果都不匹配，尝试获取全文
        logger.warning(f"未能识别的范围描述: '{range_desc}'，返回全文")
        return self.full_text
    
    def get_paragraph_count(self) -> int:
        """获取段落数量"""
        return len(self.paragraphs)
    
    def get_sentence_count(self, paragraph_index: int) -> int:
        """获取指定段落的句子数量"""
        if 0 <= paragraph_index < len(self.sentences_by_para):
            return len(self.sentences_by_para[paragraph_index])
        return 0
    
    def get_word_count(self, text: str) -> int:
        """
        计算文本的字数（不包括标点符号和空格）
        
        Args:
            text (str): 待计算字数的文本
            
        Returns:
            int: 文本的字数
        """
        # 使用正则表达式匹配中文字符和英文/数字单词，排除所有标点符号和空格
        chinese_chars = re.findall(r'[\u4e00-\u9fa5]', text)
        english_words = re.findall(r'[a-zA-Z0-9]+', text)
        
        # 中文按字计数，英文按单词计数
        return len(chinese_chars) + len(english_words)
    
    def count_characters(self, text: str) -> Dict[str, int]:
        """
        详细统计文本的字符数，包括中文字符、英文单词、数字、标点符号等
        
        Args:
            text (str): 待统计的文本
            
        Returns:
            Dict[str, int]: 包含各类字符统计的字典，包括：
                - total: 总字数（中文字符+英文单词）
                - chinese: 中文字符数
                - english_words: 英文单词数
                - punctuation: 标点符号数
                - spaces: 空格数
        """
        # 统计中文字符
        chinese_chars = re.findall(r'[\u4e00-\u9fa5]', text)
        
        # 统计英文单词
        english_words = re.findall(r'[a-zA-Z0-9]+', text)
        
        # 统计标点符号，拆分为多个简单规则避免转义问题
        # 中文标点
        chinese_punct = re.findall(r'[，。！？、；：""''【】（）《》…—]', text)
        # 英文标点
        english_punct = re.findall(r'[.,!?;:\'"{}]', text)
        # 英文括号
        brackets_punct = re.findall(r'[\(\)\[\]]', text)
        
        # 统计空格
        spaces = re.findall(r'\s', text)
        
        # 计算所有标点符号的总数
        all_punctuation = len(chinese_punct) + len(english_punct) + len(brackets_punct)
        
        return {
            "total": len(chinese_chars) + len(english_words),  # 总字数 = 中文字符 + 英文单词
            "chinese": len(chinese_chars),
            "english_words": len(english_words),
            "punctuation": all_punctuation,
            "spaces": len(spaces)
        }
    
    def count_sentences_characters(self, text: str) -> List[Dict[str, Any]]:
        """
        统计文本中每个句子的字符数
        
        Args:
            text (str): 待统计的文本
            
        Returns:
            List[Dict[str, Any]]: 每个句子的统计信息列表，每个字典包含：
                - sentence: 句子内容
                - stats: 字符统计信息（与count_characters返回格式相同）
        """
        # 分割句子
        sentences = self._split_text_to_sentences(text)
        
        # 对每个句子进行字符统计
        result = []
        for i, sentence in enumerate(sentences):
            stats = self.count_characters(sentence)
            result.append({
                "index": i + 1,
                "sentence": sentence,
                "stats": stats
            })
        
        return result
    
    def set_rule_context(self, module_name: str, rule_index: int, text: str, matching_sentence=None):
        """
        设置规则上下文，记录规则评分的文本
        
        Args:
            module_name (str): 规则模块名
            rule_index (int): 规则在模块中的索引（从1开始）
            text (str): 该规则评估的文本
            matching_sentence (str or list, optional): 匹配规则的具体句子，可以是字符串或句子列表
        """
        # 确保模块存在
        if module_name not in self.rule_text_map:
            self.rule_text_map[module_name] = [None] * 10  # 假设最多10个规则
            logger.info(f"创建新的模块规则映射: {module_name}")
        
        # 规则索引从1开始，所以我们需要-1来存储
        if 0 < rule_index <= len(self.rule_text_map[module_name]):
            self.rule_text_map[module_name][rule_index-1] = text
            # 添加空值检查
            text_preview = text[:30] + "..." if text else "空文本"
            logger.info(f"设置模块 {module_name} 中规则 {rule_index} 的评估文本: {text_preview}")
            
            # 处理匹配句子 - 优先使用传入的匹配句子
            effective_matching_sentence = None
            
            # 修改：检查matching_sentence是否为列表
            if matching_sentence is not None:
                if isinstance(matching_sentence, list):
                    logger.info(f"收到匹配句子列表: {matching_sentence}")
                    # 如果是非空列表，使用第一个元素
                    if matching_sentence:
                        effective_matching_sentence = matching_sentence[0]
                        logger.info(f"使用匹配句子列表的第一个元素: {effective_matching_sentence[:50]}...")
                    else:
                        logger.warning("收到的匹配句子列表为空")
                elif hasattr(matching_sentence, 'strip') and matching_sentence.strip():
                    # 原有的字符串处理逻辑
                    effective_matching_sentence = matching_sentence
                    logger.info(f"使用传入的匹配句子: {matching_sentence[:50]}...")
            # 如果没有有效的匹配句子但文本评分结果为正分，则视整个文本为匹配句子
            elif text and hasattr(text, 'strip') and text.strip():
                # 尝试从文本中找出匹配句子
                if len(text) > 100:  # 长文本则尝试找出特定句子
                    try_matching_sentence = self.find_matching_sentence_for_rule(module_name, rule_index, text)
                    if try_matching_sentence:
                        effective_matching_sentence = try_matching_sentence
                        logger.info(f"通过find_matching_sentence_for_rule找到匹配句子: {try_matching_sentence[:50]}...")
                    else:
                        # 找不到特定句子，则使用文本的第一个句子作为匹配句子
                        sentences = self._split_text_to_sentences(text)
                        if sentences:
                            effective_matching_sentence = sentences[0]
                            logger.info(f"使用文本的第一个句子作为匹配句子: {sentences[0][:50]}...")
                else:
                    # 短文本直接作为匹配句子
                    effective_matching_sentence = text
                    logger.info(f"短文本直接作为匹配句子: {text[:50]}...")
            
            # 存储有效的匹配句子
            if effective_matching_sentence:
                self.store_matching_sentence(module_name, rule_index, effective_matching_sentence)
                # 添加空值检查
                sentence_preview = effective_matching_sentence[:50] + "..." if len(effective_matching_sentence) > 50 else effective_matching_sentence
                logger.info(f"成功存储模块 {module_name} 中规则{rule_index}的匹配句子: {sentence_preview}")
            else:
                logger.warning(f"模块 {module_name} 中规则{rule_index}没有有效的匹配句子可存储")
        else:
            logger.warning(f"规则索引 {rule_index} 超出模块 {module_name} 的范围")
    
    def store_matching_sentence(self, module_name: str, rule_num: int, sentence: Union[str, List[str]], sentence_position: Tuple[int, int] = None):
        """
        存储规则匹配的具体句子及其位置信息
        
        Args:
            module_name (str): 规则模块名
            rule_num (int): 规则序号（从1开始）
            sentence (str or list): 匹配的句子，可以是字符串或句子列表
            sentence_position (Tuple[int, int], optional): 句子位置，格式为(段落索引, 句子索引)，索引从0开始
        """
        # 修改：处理列表类型的sentence
        effective_sentence = None
        
        if isinstance(sentence, list):
            logger.info(f"收到匹配句子列表: {sentence}")
            if sentence:  # 如果列表非空，使用第一个元素
                effective_sentence = sentence[0]
                logger.info(f"使用列表中的第一个句子: {effective_sentence[:50] if len(effective_sentence) > 50 else effective_sentence}")
            else:
                logger.warning(f"尝试存储空句子列表，模块: {module_name}, 规则: {rule_num}")
                return
        else:
            # 原有的字符串处理逻辑
            if not sentence or not hasattr(sentence, 'strip') or not sentence.strip():
                logger.warning(f"尝试存储空句子，模块: {module_name}, 规则: {rule_num}")
                return
            effective_sentence = sentence
            
        if module_name not in self.rule_matching_sentences:
            self.rule_matching_sentences[module_name] = {}
            logger.info(f"创建模块 {module_name} 的匹配句子映射")
        
        # 存储句子内容
        self.rule_matching_sentences[module_name][rule_num] = effective_sentence
        
        # 存储句子位置信息（如果有）
        if sentence_position is not None:
            # 如果还没有存储位置信息的字典，创建一个
            if not hasattr(self, 'rule_matching_sentence_positions'):
                self.rule_matching_sentence_positions = {}
            
            if module_name not in self.rule_matching_sentence_positions:
                self.rule_matching_sentence_positions[module_name] = {}
                
            self.rule_matching_sentence_positions[module_name][rule_num] = sentence_position
            
            # 记录位置信息
            paragraph_index, sentence_index = sentence_position
            position_desc = self.translate_position_to_description(paragraph_index, sentence_index)
            logger.info(f"存储模块 {module_name} 中规则{rule_num}的句子位置: {position_desc}")
        
        # 添加空值检查，虽然前面已经检查过，但保险起见再次检查
        sentence_preview = effective_sentence[:50] + "..." if len(effective_sentence) > 50 else effective_sentence
        logger.info(f"成功存储模块 {module_name} 中规则{rule_num}的匹配句子: {sentence_preview}")
    
    def find_matching_sentence_for_rule(self, module_name: str, rule_num: int, text: str) -> str:
        """
        查找与指定规则匹配的句子
        
        Args:
            module_name (str): 模块名称
            rule_num (int): 规则编号
            text (str): 待查找的文本
            
        Returns:
            str: 匹配的句子，如果没有找到则返回空字符串
        """
        # 首先检查是否有存储的匹配句子
        if module_name in self.rule_matching_sentences and rule_num in self.rule_matching_sentences[module_name]:
            stored_sentence = self.rule_matching_sentences[module_name][rule_num]
            logger.info(f"直接使用存储的匹配句子: {stored_sentence[:50] if len(stored_sentence) > 50 else stored_sentence}")
            return stored_sentence
            
        # 如果没有存储的匹配句子，将文本分割成句子
        sentences = self._split_text_to_sentences(text)
        if not sentences:
            return ""
        
        # 默认情况：如果没有特定的匹配逻辑，返回段落的第一句话
        if sentences:
            logger.info(f"没有存储的匹配句子，返回第一句: {sentences[0][:30]}...")
            return sentences[0]
        
        # 如果没有句子，返回空字符串
        logger.warning(f"未找到任何匹配句子")
        return ""
    
    def set_current_rule_module(self, module_name: str):
        """
        设置当前处理的规则模块
        
        Args:
            module_name (str): 当前规则模块名
        """
        self.current_rule_module = module_name
        logger.info(f"设置当前规则模块: {module_name}")
        
        # 尝试从模块名解析出段落信息并预先加载相关段落
        if module_name == "标题":
            logger.info("当前模块为标题，预加载标题文本")
        elif "首段" in module_name or "第一段" in module_name:
            if len(self.paragraphs) > 0:
                logger.info(f"当前模块与首段相关，预加载首段文本")
        elif "尾段" in module_name or "最后一段" in module_name:
            if len(self.paragraphs) > 0:
                logger.info(f"当前模块与尾段相关，预加载尾段文本")
        else:
            # 尝试匹配阿拉伯数字的段落
            para_match = re.search(r'第(\d+)段', module_name)
            if para_match:
                para_num = int(para_match.group(1))
                para_index = para_num - 1  # 转为0-索引
                if 0 <= para_index < len(self.paragraphs):
                    logger.info(f"当前模块与第{para_num}段相关，预加载该段落文本")
            else:
                # 尝试中文数字
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                para_match = re.search(r'第([一二三四五六七八九十]+)段', module_name)
                if para_match:
                    chinese_num = para_match.group(1)
                    if chinese_num in chinese_num_map:
                        para_num = chinese_num_map[chinese_num]
                        para_index = para_num - 1  # 转为0-索引
                        if 0 <= para_index < len(self.paragraphs):
                            logger.info(f"当前模块与第{para_num}段相关，预加载该段落文本")
    
    def _split_text_to_sentences(self, text: str) -> List[str]:
        """
        将文本分割成句子
        
        Args:
            text (str): 待分割的文本
            
        Returns:
            List[str]: 句子列表
        """
        # 检查输入文本是否为空
        if not text:
            logger.warning("尝试从空文本中分割句子")
            return []
            
        # 将文本分割成句子
        sentences = []
        for paragraph in self._split_paragraphs(text):
            sentences.extend(self._split_sentences(paragraph))
            
        return sentences
    
    def llm_extract_text_by_range(self, range_desc: str, llm_interface, rule_index: int = 0) -> str:
        """
        使用大模型提取符合范围描述的文本片段
        
        Args:
            range_desc (str): 范围描述，如"第二段，规则2的句子之后的一句"是递进关系，而"第二句；规则1的句子"是并列关系
            llm_interface: LLMInterface实例，用于调用大模型
            rule_index (int): 当前规则索引，用于日志记录
            
        Returns:
            str: 提取的文本片段
        """
        # 添加明显的开始标记
        print("\n")
        print("=" * 80)
        print("■■■■■■■■■■■■■■■■ 开始文本范围提取 ■■■■■■■■■■■■■■■■")
        print(f"【范围描述】: {range_desc}")
        print("-" * 80)
        
        # 检查是否已经处理过相同的范围描述
        if range_desc in self.llm_extracted_texts:
            logger.info(f"使用缓存的大模型提取结果，范围描述: '{range_desc}'")
            result = self.llm_extracted_texts[range_desc]
            
            # 显示提取结果
            print(f"【缓存的提取结果】: {result[:200]}..." if len(result) > 200 else result)
            print("■■■■■■■■■■■■■■■■ 文本范围提取完成(使用缓存) ■■■■■■■■■■■■■■■■")
            print("=" * 80)
            return result
            
        # 检查是否包含并列范围描述（通过分号或顿号分隔）
        parallel_range_separators = [";", "；", "、"]
        is_parallel_ranges = False
        
        for separator in parallel_range_separators:
            if separator in range_desc:
                is_parallel_ranges = True
                break
        
        if is_parallel_ranges:
            logger.info(f"检测到并列多范围描述: '{range_desc}'")
            
            # 尝试使用多种可能的分隔符来拆分范围描述
            ranges = []
            for separator in parallel_range_separators:
                if separator in range_desc:
                    parts = range_desc.split(separator)
                    parts = [part.strip() for part in parts if part.strip()]
                    ranges.extend(parts)
            
            # 移除重复的范围描述
            ranges = list(dict.fromkeys(ranges))
            
            logger.info(f"拆分出的并列范围描述: {ranges}")
            
            # 为每个范围单独提取文本
            results = []
            for i, single_range in enumerate(ranges):
                logger.info(f"处理第 {i+1}/{len(ranges)} 个范围描述: '{single_range}'")
                
                extracted_text = self._extract_single_range(single_range, llm_interface, rule_index)
                if extracted_text:
                    # 对于多范围提取，使用清晰的格式标记每个部分
                    results.append(f"{single_range}：{extracted_text}")
                else:
                    results.append(f"{single_range}：未能提取到文本")
            
            # 合并结果，用换行分隔
            combined_result = "\n".join(results)
            logger.info(f"并列多范围提取结果: \n{combined_result}")
            
            # 缓存组合结果
            self.llm_extracted_texts[range_desc] = combined_result
            
            # 显示提取结果
            print("\n【并列多范围提取结果】:")
            for result_line in results:
                print(f"  {result_line}")
            print("■■■■■■■■■■■■■■■■ 文本范围提取完成 ■■■■■■■■■■■■■■■■")
            print("=" * 80)
            
            return combined_result
        else:
            # 检查是否包含递进关系范围描述（通过逗号分隔）
            sequential_range_separators = [",", "，"]
            is_sequential_ranges = False
            
            for separator in sequential_range_separators:
                if separator in range_desc:
                    is_sequential_ranges = True
                    break
                    
            if is_sequential_ranges:
                logger.info(f"检测到递进多范围描述: '{range_desc}'")
                
                # 使用递进式范围处理，先处理主范围，然后在其结果上处理子范围
                # 根据当前模块确定起始文本，而不是使用全文
                current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
                current_text = None
                
                # 如果有当前模块，尝试从模块名中提取段落
                if current_module:
                    # 支持中文数字和阿拉伯数字
                    chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                    
                    # 检查是否是标题模块
                    if current_module == "标题":
                        logger.info(f"递进多范围 - 当前模块是标题，使用标题文本作为起点")
                        current_text = self.title or ""
                    
                    # 先尝试阿拉伯数字
                    para_match = re.search(r'第(\d+)段', current_module)
                    if para_match:
                        para_num = int(para_match.group(1))
                        para_index = para_num - 1  # 转为0-索引
                        if 0 <= para_index < len(self.paragraphs):
                            logger.info(f"递进多范围 - 从模块名 {current_module} 提取段落索引: {para_index}，使用该段落作为起点")
                            current_text = self.paragraphs[para_index]
                    
                    # 尝试中文数字
                    if current_text is None:
                        para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                        if para_match:
                            chinese_num = para_match.group(1)
                            if chinese_num in chinese_num_map:
                                para_num = chinese_num_map[chinese_num]
                                para_index = para_num - 1  # 转为0-索引
                                if 0 <= para_index < len(self.paragraphs):
                                    logger.info(f"递进多范围 - 从模块名 {current_module} 提取段落索引: {para_index}，使用该段落作为起点")
                                    current_text = self.paragraphs[para_index]
                    
                    # 检查是否是首段或第一段
                    if current_text is None and ("首段" in current_module or "第一段" in current_module):
                        if len(self.paragraphs) > 0:
                            logger.info(f"递进多范围 - 当前模块是首段，使用第一段作为起点")
                            current_text = self.paragraphs[0]
                    
                    # 检查是否是尾段或最后一段
                    if current_text is None and ("尾段" in current_module or "最后一段" in current_module):
                        if len(self.paragraphs) > 0:
                            logger.info(f"递进多范围 - 当前模块是尾段，使用最后一段作为起点")
                            current_text = self.paragraphs[-1]
                
                # 如果无法从模块名确定段落，使用全文（保留原行为作为后备）
                if current_text is None:
                    logger.warning(f"递进多范围 - 无法从模块名 {current_module} 确定段落，使用全文作为起点")
                    current_text = self.full_text
                
                base_context = ""
                
                # 尝试使用逗号拆分范围描述
                parts = None
                for separator in sequential_range_separators:
                    if separator in range_desc:
                        parts = [part.strip() for part in range_desc.split(separator) if part.strip()]
                        break
                
                if not parts:
                    logger.warning(f"无法拆分递进范围描述: '{range_desc}'")
                    
                    # 显示错误信息
                    print("【错误】: 无法拆分递进范围描述")
                    print("■■■■■■■■■■■■■■■■ 文本范围提取失败 ■■■■■■■■■■■■■■■■")
                    print("=" * 80)
                    
                    return ""
                
                logger.info(f"拆分出的递进范围描述: {parts}")
                
                # 创建一个规则字典，只用于文本提取
                extraction_rule = {"规则": "精确提取", "范围": ""}
                
                # 添加规则参考信息
                references = {}
                
                # 预先扫描所有部分，收集规则引用
                for part in parts:
                    # 检查是否包含规则引用
                    rule_matches = re.finditer(r'规则(\d+)的句子', part)
                    for rule_match in rule_matches:
                        rule_num = int(rule_match.group(1))
                        current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
                        
                        if current_module and current_module in self.rule_matching_sentences and rule_num in self.rule_matching_sentences[current_module]:
                            rule_sentence = self.rule_matching_sentences[current_module][rule_num]
                            references[f"规则{rule_num}的句子"] = rule_sentence
                            logger.info(f"找到规则{rule_num}的句子参考: '{rule_sentence[:50]}...'")
                
                # 如果有规则引用，添加到提取规则中
                if references:
                    extraction_rule["规则参考"] = references
                    logger.info(f"添加规则参考信息: {json.dumps(references, ensure_ascii=False)[:200]}...")
                
                # 逐步缩小范围
                for i, part in enumerate(parts):
                    logger.info(f"处理递进范围部分 {i+1}/{len(parts)}: '{part}'")
                    
                    if i == 0:
                        # 第一部分直接提取
                        base_text = self._extract_single_range(part, llm_interface, rule_index)
                        if not base_text:
                            logger.warning(f"无法提取基础范围 '{part}' 的文本")
                            
                            # 显示错误信息
                            print(f"【错误】: 无法提取基础范围 '{part}' 的文本")
                            print("■■■■■■■■■■■■■■■■ 文本范围提取失败 ■■■■■■■■■■■■■■■■")
                            print("=" * 80)
                            
                            return ""
                        
                        current_text = base_text
                        base_context = part
                        logger.info(f"提取基础范围文本: '{current_text[:50]}...'")
                    else:
                        # 后续部分在前一部分结果的基础上提取
                        combined_part = f"{base_context}中的{part}"
                        logger.info(f"构建子范围描述: '{combined_part}'")
                        
                        # 使用大模型在当前文本上进一步提取
                        extraction_rule["范围"] = part
                        prompt = llm_interface._build_prompt("文本范围提取", extraction_rule, current_text)
                        response = llm_interface._call_openai(prompt)
                        result = llm_interface._parse_response(response, extraction_rule)
                        
                        # 记录文本范围提取到api_debug.log
                        extraction_rule["is_text_extraction"] = True  # 标记为文本提取任务
                        llm_interface._log_api_debug_info(prompt, response, result, extraction_rule, is_error=False, rule_index=rule_index)
                        
                        # 获取提取结果
                        if "comment" in result:
                            current_text = result["comment"]
                            logger.info(f"在子范围 '{part}' 上提取文本: '{current_text[:50]}...'")
                        else:
                            logger.warning(f"无法在范围 '{combined_part}' 上提取文本")
                            
                            # 显示错误信息
                            print(f"【错误】: 无法在范围 '{combined_part}' 上提取文本")
                            print("■■■■■■■■■■■■■■■■ 文本范围提取失败 ■■■■■■■■■■■■■■■■")
                            print("=" * 80)
                            
                            return ""
                
                # 缓存最终结果
                logger.info(f"递进多范围提取最终结果: '{current_text[:100]}...'")
                self.llm_extracted_texts[range_desc] = current_text
                
                # 显示提取结果
                print("\n【递进多范围最终提取结果】:")
                print(f"  {current_text}")
                print("■■■■■■■■■■■■■■■■ 文本范围提取完成 ■■■■■■■■■■■■■■■■")
                print("=" * 80)
                
                return current_text
            else:
                # 单一范围描述，调用原有逻辑处理
                result = self._extract_single_range(range_desc, llm_interface, rule_index)
                
                # 显示提取结果
                print("\n【单一范围提取结果】:")
                print(f"  {result}")
                print("■■■■■■■■■■■■■■■■ 文本范围提取完成 ■■■■■■■■■■■■■■■■")
                print("=" * 80)
                
                return result
    
    def _extract_single_range(self, range_desc: str, llm_interface, rule_index: int = 0) -> str:
        """
        处理单一范围描述的提取逻辑（从原有的llm_extract_text_by_range方法迁移过来）
        
        Args:
            range_desc (str): 单一范围描述
            llm_interface: LLMInterface实例
            rule_index (int): 规则索引
            
        Returns:
            str: 提取的文本
        """
        # 简单范围描述可以直接使用原有方法处理
        if re.match(r'^(全文|整篇文章|首段|第一段|第二段|第三段|第四段|尾段|最后一段|标题|整篇文章第一行)$', range_desc):
            logger.info(f"简单范围描述，使用原有方法处理: '{range_desc}'")
            return self.get_text_by_range(range_desc)
            
        # 处理"规则X的句子"格式 - 从存储的规则匹配句子中获取
        # 修改: 使用更宽松的匹配模式，包括"规则X的句子"和各种变体
        if "规则" in range_desc and "的句子" in range_desc:
            rule_match = re.search(r'规则(\d+)的句子', range_desc)
            if rule_match:
                rule_num = int(rule_match.group(1))
                current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
                
                # 检查是否是完全匹配的"规则X的句子"格式
                if re.match(r'^规则\d+的句子$', range_desc):
                    logger.info(f"精确匹配到'规则X的句子'模式: '{range_desc}'")
                    
                    if current_module and current_module in self.rule_matching_sentences and rule_num in self.rule_matching_sentences[current_module]:
                        matching_sentence = self.rule_matching_sentences[current_module][rule_num]
                        logger.info(f"从存储的规则匹配句子中提取 '{current_module}' 模块的规则 {rule_num} 匹配句子: '{matching_sentence[:50]}...'")
                        
                        # 缓存提取结果
                        self.llm_extracted_texts[range_desc] = matching_sentence
                        return matching_sentence
                    else:
                        logger.warning(f"无法找到模块 '{current_module}' 的规则 {rule_num} 匹配句子")
                        return ""
                # 新增：检查是否匹配"范围": "规则X的句子"这种格式（规则定义中的范围字段）
                elif range_desc.strip() == f"规则{rule_num}的句子":
                    logger.info(f"匹配到作为范围值的'规则{rule_num}的句子'模式")
                    
                    if current_module and current_module in self.rule_matching_sentences and rule_num in self.rule_matching_sentences[current_module]:
                        matching_sentence = self.rule_matching_sentences[current_module][rule_num]
                        logger.info(f"直接使用存储的matching_sentence，无需调用大模型: '{matching_sentence[:50]}...'")
                        
                        # 缓存提取结果
                        self.llm_extracted_texts[range_desc] = matching_sentence
                        return matching_sentence
                    else:
                        logger.warning(f"无法找到模块 '{current_module}' 的规则 {rule_num} 匹配句子")
                        return ""
        
        # 检查是否是包含"之后"的规则引用模式
        if "规则" in range_desc and "的句子之后" in range_desc:
            logger.info(f"检测到'规则X的句子之后'模式，将使用大模型提取: '{range_desc}'")
        
        # 识别当前处理的模块上下文，用于解释相对位置
        current_module = self.current_rule_module if hasattr(self, 'current_rule_module') else None
        current_paragraph_context = None
        
        # 从模块名称中提取段落信息
        if current_module:
            para_match = re.search(r'(首段|第[一二三四五六七八九十\d]+段|尾段|最后一段)', current_module)
            if para_match:
                current_paragraph_context = para_match.group(1)
                logger.info(f"从当前模块 '{current_module}' 中识别到段落上下文: {current_paragraph_context}")
        
        # 处理基于当前段落上下文的相对位置描述（如"第一句"、"第二句"等）
        if current_paragraph_context and re.match(r'^第[一二三四五六七八九十\d]+句$', range_desc):
            logger.info(f"检测到相对位置描述 '{range_desc}' 在段落上下文 '{current_paragraph_context}' 中")
            
            # 确定段落索引
            para_index = 0
            if current_paragraph_context == "首段":
                para_index = 0
            elif current_paragraph_context == "尾段" or current_paragraph_context == "最后一段":
                para_index = len(self.paragraphs) - 1
            else:
                para_num_match = re.search(r'第([一二三四五六七八九十\d]+)段', current_paragraph_context)
                if para_num_match:
                    para_num = para_num_match.group(1)
                    # 处理中文数字
                    chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                    if para_num in chinese_num_map:
                        para_index = chinese_num_map[para_num] - 1
                    else:
                        try:
                            para_index = int(para_num) - 1
                        except ValueError:
                            # 无法解析为数字，保持默认值
                            pass
            
            # 确定句子索引
            sentence_index = 0
            sent_num_match = re.search(r'第([一二三四五六七八九十\d]+)句', range_desc)
            if sent_num_match:
                sent_num = sent_num_match.group(1)
                # 处理中文数字
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                if sent_num in chinese_num_map:
                    sentence_index = chinese_num_map[sent_num] - 1
                else:
                    try:
                        sentence_index = int(sent_num) - 1
                    except ValueError:
                        sentence_index = 0
            
            # 获取段落中的指定句子
            if 0 <= para_index < len(self.paragraphs):
                # 确保句子索引在有效范围内
                if 0 <= para_index < len(self.sentences_by_para) and self.sentences_by_para[para_index]:
                    if 0 <= sentence_index < len(self.sentences_by_para[para_index]):
                        extracted_sentence = self.sentences_by_para[para_index][sentence_index]
                        logger.info(f"从 {current_paragraph_context} 中提取第 {sent_num} 句: '{extracted_sentence}'")
                        
                        # 缓存提取结果
                        self.llm_extracted_texts[range_desc] = extracted_sentence
                        return extracted_sentence
                    else:
                        logger.warning(f"{current_paragraph_context} 中没有足够的句子 (只有 {len(self.sentences_by_para[para_index])} 句)")
                else:
                    logger.warning(f"无法获取段落 {para_index} 的句子列表")
            else:
                logger.warning(f"段落索引 {para_index} 超出范围 (共 {len(self.paragraphs)} 段)")
        
        # 对于更复杂的范围描述，使用LLM提取
        logger.info(f"复杂范围描述，使用LLM提取: '{range_desc}'")
        
        # 检查是否有规则依赖（如"规则X的句子之后的一句"）
        has_rule_reference = re.search(r'规则\d+的句子', range_desc)
        references = {}
        
        if has_rule_reference:
            # 收集所有引用的规则句子
            rule_matches = re.finditer(r'规则(\d+)的句子', range_desc)
            for rule_match in rule_matches:
                rule_num = int(rule_match.group(1))
                if current_module and current_module in self.rule_matching_sentences and rule_num in self.rule_matching_sentences[current_module]:
                    rule_sentence = self.rule_matching_sentences[current_module][rule_num]
                    references[f"规则{rule_num}的句子"] = rule_sentence
                    logger.info(f"为复杂范围提供规则{rule_num}的句子参考: '{rule_sentence[:50]}...'")
        
        # 创建专门用于提取的规则字典
        extraction_rule = {
            "规则": "精确提取",
            "范围": range_desc,
            "is_text_extraction": True
        }
        
        # 如果有当前模块名称，添加到规则路径中以便传递段落上下文
        if current_module:
            extraction_rule["path"] = current_module
            logger.info(f"在提取规则中添加路径信息: {current_module}")
            
            # 记录日志，显示我们正在添加段落上下文
            if range_desc and re.search(r'第[一二三四五六七八九十\d]+句', range_desc) and not re.search(r'段|首段|尾段', range_desc):
                logger.info(f"检测到'第几句'范围描述，已添加段落上下文 '{current_module}' 用于范围 '{range_desc}'")
                print(f"[范围提取] 为范围 '{range_desc}' 添加段落上下文: {current_module}", flush=True)
        elif range_desc and re.search(r'第[一二三四五六七八九十\d]+句', range_desc) and not re.search(r'段|首段|尾段', range_desc):
            # 如果是"第几句"格式但没有段落上下文，记录警告
            logger.warning(f"检测到'第几句'范围描述 '{range_desc}'，但没有段落上下文信息")
            print(f"[范围提取警告] 范围 '{range_desc}' 没有段落上下文，可能会导致提取整篇文章的句子", flush=True)
        
        # 如果有规则引用，添加到提取规则中
        if references:
            extraction_rule["规则参考"] = references
            logger.info(f"在提取规则中添加规则参考信息: {json.dumps(references, ensure_ascii=False)[:200]}...")
        
        # 确定应该提交给大模型的文本内容
        # 优先使用当前段落的内容，而不是整篇文章
        text_content_to_submit = self.full_text  # 默认使用全文
        
        # 从模块名或路径中提取段落信息
        para_index = None
        if current_module:
            # 检查是否是标题模块
            if current_module == "标题":
                text_content_to_submit = self.title
                logger.info(f"使用标题内容进行文本提取")
            
            # 处理首段
            elif "首段" in current_module or "第一段" in current_module:
                if len(self.paragraphs) > 0:
                    text_content_to_submit = self.paragraphs[0]
                    para_index = 0
                    logger.info(f"使用首段内容进行文本提取")
            
            # 处理尾段
            elif "尾段" in current_module or "最后一段" in current_module:
                if len(self.paragraphs) > 0:
                    text_content_to_submit = self.paragraphs[-1]
                    para_index = len(self.paragraphs) - 1
                    logger.info(f"使用尾段内容进行文本提取")
            
            # 处理特定段落（阿拉伯数字）
            else:
                para_match = re.search(r'第(\d+)段', current_module)
                if para_match:
                    para_num = int(para_match.group(1))
                    para_index = para_num - 1  # 转为0-索引
                    if 0 <= para_index < len(self.paragraphs):
                        text_content_to_submit = self.paragraphs[para_index]
                        logger.info(f"使用第{para_num}段内容进行文本提取")
                
                # 处理特定段落（中文数字）
                if para_index is None:
                    chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                    para_match = re.search(r'第([一二三四五六七八九十]+)段', current_module)
                    if para_match:
                        chinese_num = para_match.group(1)
                        if chinese_num in chinese_num_map:
                            para_num = chinese_num_map[chinese_num]
                            para_index = para_num - 1  # 转为0-索引
                            if 0 <= para_index < len(self.paragraphs):
                                text_content_to_submit = self.paragraphs[para_index]
                                logger.info(f"使用第{chinese_num}段内容进行文本提取")
        
        # 如果未能确定段落，但有范围中包含段落信息
        if para_index is None and range_desc:
            # 检查范围描述中是否包含段落信息
            para_match = re.search(r'第(\d+)段', range_desc)
            if para_match:
                para_num = int(para_match.group(1))
                para_index = para_num - 1  # 转为0-索引
                if 0 <= para_index < len(self.paragraphs):
                    text_content_to_submit = self.paragraphs[para_index]
                    logger.info(f"从范围描述中提取段落信息，使用第{para_num}段内容进行文本提取")
            
            # 中文数字形式
            if para_index is None:
                chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
                para_match = re.search(r'第([一二三四五六七八九十]+)段', range_desc)
                if para_match:
                    chinese_num = para_match.group(1)
                    if chinese_num in chinese_num_map:
                        para_num = chinese_num_map[chinese_num]
                        para_index = para_num - 1  # 转为0-索引
                        if 0 <= para_index < len(self.paragraphs):
                            text_content_to_submit = self.paragraphs[para_index]
                            logger.info(f"从范围描述中提取段落信息，使用第{chinese_num}段内容进行文本提取")
        
        # 记录使用的文本内容长度
        logger.info(f"提交给大模型的文本内容长度: {len(text_content_to_submit)} 字符")
        
        # 构建提示词并调用大模型
        prompt = llm_interface._build_prompt("文本范围提取", extraction_rule, text_content_to_submit)
        response = llm_interface._call_openai(prompt)
        
        # 解析响应
        result = llm_interface._parse_response(response, extraction_rule)
        
        # 记录文本范围提取到api_debug.log
        extraction_rule["is_text_extraction"] = True  # 标记为文本提取任务
        llm_interface._log_api_debug_info(prompt, response, result, extraction_rule, is_error=False, rule_index=rule_index)
        
        # 提取结果
        if "comment" in result:
            extracted_text = result["comment"]
            logger.info(f"使用LLM提取范围 '{range_desc}' 的文本: '{extracted_text[:100]}...'")
            
            # 缓存提取结果
            self.llm_extracted_texts[range_desc] = extracted_text
            return extracted_text
        else:
            logger.warning(f"LLM无法提取范围 '{range_desc}' 的文本，解析结果: {result}")
            return ""
    
    def _check_essay_title(self):
        """
        检查作文是否有标题，并记录相关信息
        """
        if not self.essay_text:
            logger.warning("作文内容为空，无法检查标题")
            return
        
        # 不再重复处理文本，而是使用已经在TextProcessor中处理好的信息
        title = self.text_processor.title
        paragraphs = self.text_processor.paragraphs
        sentences_by_para = self.text_processor.sentences_by_para
        
        logger.info(f"作文第一行（标题）: '{title}'")
        
        # 如果有段落，记录第一段的摘要信息
        if paragraphs:
            first_para = paragraphs[0]
            para_preview = first_para[:50] + "..." if len(first_para) > 50 else first_para
            logger.info(f"作文第一段: '{para_preview}'")
            
            # 记录第一段的句子数量
            if 0 < len(sentences_by_para):
                first_para_sentences = sentences_by_para[0]
                logger.info(f"第一段包含 {len(first_para_sentences)} 个句子")
                
                # 记录前4个句子（如果存在）
                for i, sent in enumerate(first_para_sentences[:4]):
                    logger.info(f"第一段第{i+1}句: '{sent}'")
        
        # 将文章第一行设置为标题的上下文（保留这个操作以确保兼容性）
        self.text_processor.title = title
    
    def grade_essay(self) -> str:
        """
        评分作文
        
        Returns:
            str: 评分结果文本
        """
        # 检查作文标题
        self._check_essay_title()
        
        # 获取所有可评分的规则
        scorable_rules = self.rule_processor.get_all_scorable_rules()
        
        if self.verbose:
            logger.info(f"找到 {len(scorable_rules)} 条可评分规则")
        
        # 添加一个计数器跟踪通用评语出现次数
        generic_comment_counter = {"未提供需要评分的文本片段": 0}
        
        # 按模块分组规则
        rules_by_module = {}
        for rule_item in scorable_rules:
            module_name = self._extract_rule_module(rule_item["path"])
            if module_name not in rules_by_module:
                rules_by_module[module_name] = []
            rules_by_module[module_name].append(rule_item)
        
        # 添加日志，显示规则按模块分组情况
        logger.info(f"规则按模块分组情况:")
        for module_name, module_rules in rules_by_module.items():
            logger.info(f"模块 '{module_name}' 包含 {len(module_rules)} 条规则")
            for idx, rule in enumerate(module_rules, 1):
                logger.info(f"  - 规则 {idx}: 路径={rule['path']}, 范围={rule['rule'].get('范围', '全文')}")
        
        # 全局存储复合规则的评估结果，用于跨模块复用
        complex_rule_cache = {}
        
        # 对每个模块内的规则进行排序并评分
        for module_name, module_rules in rules_by_module.items():
            # 按照原始顺序排序，而不是按路径
            module_rules.sort(key=lambda x: x.get("order", 999999))
            
            # 设置当前规则模块
            self.text_processor.set_current_rule_module(module_name)
            
            # 存储规则依赖关系的映射，用于后续处理
            rule_dependencies = {}
            
            # 记录当前模块中规则的顺序
            logger.info(f"模块 '{module_name}' 中的规则顺序:")
            for idx, rule in enumerate(module_rules, 1):
                logger.info(f"  规则 {idx}: {rule['path']}")
            
            # 第一轮：对模块内的规则逐个评分，并找出匹配句子
            for rule_index, rule_item in enumerate(module_rules, 1):
                rule_path = rule_item["path"]
                rule_data = rule_item["rule"]
                
                # 如果规则中有"范围"字段，从作文中提取相应文本
                text_range = rule_data.get("范围", "全文")
                
                # 检查范围是否依赖于其他规则
                depends_on_rule = None
                is_complex_rule = False
                
                logger.info(f"处理规则 {rule_index}/{len(module_rules)} (路径: {rule_path}), 范围描述: '{text_range}'")
                
                # 检查是否是单一规则依赖
                rule_match = re.search(r'规则(\d+)的句子', text_range)
                if rule_match:
                    depends_on_rule = int(rule_match.group(1))
                    rule_dependencies[rule_index] = depends_on_rule
                    logger.info(f"规则 {rule_index} (路径: {rule_path}) 依赖于模块内的规则 {depends_on_rule}")
                    
                    # 获取依赖规则的匹配句子位置信息
                    source_position = None
                    if hasattr(self.text_processor, 'rule_matching_sentence_positions') and \
                       module_name in self.text_processor.rule_matching_sentence_positions and \
                       depends_on_rule in self.text_processor.rule_matching_sentence_positions[module_name]:
                        source_position = self.text_processor.rule_matching_sentence_positions[module_name][depends_on_rule]
                        logger.info(f"获取到依赖规则 {depends_on_rule} 的句子位置: {source_position}")
                
                # 检查是否是复合规则依赖（如"规则2或4或6的句子之后"）
                complex_rule_match = re.search(r'规则([\d或]+)的句子之后', text_range)
                if complex_rule_match:
                    is_complex_rule = True
                    rule_nums_str = complex_rule_match.group(1)
                    logger.info(f"规则 {rule_index} (路径: {rule_path}) 是复合依赖规则: 依赖于规则 {rule_nums_str}")
                    
                    # 尝试从全局缓存中获取这个复合规则的评估结果
                    cache_key = f"复合规则:{rule_nums_str}"
                    if cache_key in complex_rule_cache:
                        cached_result = complex_rule_cache[cache_key]
                        logger.info(f"使用缓存的复合规则评估结果: {cache_key}")
                        
                        # 复用缓存的评估结果
                        self.score_manager.add_score(
                            rule_path,
                            cached_result["text"],
                            cached_result["score"],
                            cached_result["comment"],
                            cached_result.get("thought_process"),
                            text_range  # 添加范围描述
                        )
                        continue
                    
                    # 在当前模块内检查是否已经评估过类似的规则
                    already_processed = False
                    for prev_idx, prev_rule in enumerate(module_rules[:rule_index-1], 1):
                        prev_range = prev_rule.get("rule", {}).get("范围", "")
                        if prev_range == text_range:
                            logger.info(f"跳过重复的复合规则评估: 规则 {rule_index} 与规则 {prev_idx} 有相同的范围描述: {text_range}")
                            already_processed = True
                            
                            # 复用之前在该模块中的评分结果
                            for score_item in self.score_manager.scores:
                                if score_item["rule_path"] == prev_rule["path"]:
                                    self.score_manager.add_score(
                                        rule_path,
                                        score_item["text"],
                                        score_item["score"],
                                        score_item["comment"],
                                        score_item.get("thought_process"),
                                        text_range  # 添加范围描述
                                    )
                                    logger.info(f"复用规则 {prev_idx} 的评分结果: {score_item['score']}分 - {score_item['comment']}")
                                    break
                            break
                    
                    if already_processed:
                        continue
                
                # 使用大模型提取符合范围描述的文本
                # 对于复杂的范围描述（包含规则引用或特定句子位置描述），使用大模型提取
                text_to_evaluate = ""
                is_complex_range = re.search(r'规则|句子之后|第.*句|尾句|末句|最后一句', text_range) is not None
                
                if is_complex_range:
                    logger.info(f"使用大模型提取复杂范围描述的文本: '{text_range}'")
                    text_to_evaluate = self.text_processor.llm_extract_text_by_range(
                        text_range, 
                        self.llm_interface,
                        rule_index
                    )
                else:
                    # 对于简单范围描述，使用原始方法提取
                    logger.info(f"使用原有方法提取简单范围描述的文本: '{text_range}'")
                    text_to_evaluate = self.text_processor.get_text_by_range(text_range)
                
                if not text_to_evaluate:
                    logger.warning(f"无法为规则 {rule_path} 提取有效文本（范围：{text_range}）")
                    logger.error(f"⚠️ 发现空文本片段! 模块: {module_name}, 规则: {rule_index}, 路径: {rule_path}, 范围描述: '{text_range}'")
                    
                    # 添加一个通用评语，这里可能会导致重复
                    comment = "未提供需要评分的文本片段，无法判断是否满足评分标准，因此得0分。"
                    generic_comment_counter["未提供需要评分的文本片段"] += 1
                    logger.error(f"⚠️ 添加通用评语: '{comment}' (第{generic_comment_counter['未提供需要评分的文本片段']}次)")
                    
                    self.score_manager.add_score(
                        rule_path, 
                        "", 
                        0, 
                        comment,
                        None,  # 空文本片段没有思考过程
                        text_range  # 添加范围描述
                    )
                    continue
                
                if self.verbose:
                    logger.info(f"评分规则：{rule_path}")
                    logger.info(f"评分范围：{text_range}")
                    logger.info(f"待评分文本：{text_to_evaluate[:50]}...")
                
                # 调用大模型进行评分
                # 将规则路径添加到规则数据中
                rule_data_with_path = rule_data.copy()
                rule_data_with_path["path"] = rule_path
                
                eval_result = self.llm_interface.evaluate_text(
                    self.title, rule_data_with_path, text_to_evaluate, rule_index=rule_index
                )
                
                # 如果是复合规则，将结果存入全局缓存
                if is_complex_rule and complex_rule_match:
                    rule_nums_str = complex_rule_match.group(1)
                    cache_key = f"复合规则:{rule_nums_str}"
                    complex_rule_cache[cache_key] = {
                        "text": text_to_evaluate,
                        "score": eval_result["score"],
                        "comment": eval_result["comment"],
                        "thought_process": eval_result.get("thought_process")
                    }
                    logger.info(f"缓存复合规则评估结果: {cache_key}")
                
                # 根据评分结果判断是否找到匹配句子
                matching_sentence = None
                sentence_position = None  # 存储句子位置信息
                
                # 如果API返回了匹配句子
                if "matching_sentence" in eval_result:
                    matching_sentence = eval_result["matching_sentence"]
                    if matching_sentence:  # 添加空值检查
                        logger.info(f"API返回的匹配句子: {matching_sentence[:50]}...")
                    else:
                        logger.info("API返回的匹配句子为None或空字符串")
                    
                    # 检查是否返回了句子位置信息
                    if "sentence_position" in eval_result:
                        position_desc = eval_result["sentence_position"]
                        logger.info(f"API返回的句子位置: {position_desc}")
                        
                        # 解析句子位置描述，获取具体的段落索引和句子索引
                        current_para_index = 0  # 默认使用当前段落
                        
                        # 从模块名中尝试获取段落索引
                        module_para_match = re.search(r'第(\d+)段', module_name)
                        if module_para_match:
                            current_para_index = int(module_para_match.group(1)) - 1  # 转为0-索引
                            logger.info(f"从模块名 '{module_name}' 提取段落索引: {current_para_index}")
                        
                        # 解析位置描述
                        try:
                            sentence_position = self.text_processor.parse_sentence_position_description(
                                position_desc, current_para_index
                            )
                            para_idx, sent_idx = sentence_position
                            logger.info(f"解析的句子位置: 第{para_idx+1}段第{sent_idx+1}句")
                            
                            # 验证句子位置是否有效
                            sentence_at_position = self.text_processor.get_sentence_by_position(para_idx, sent_idx)
                            if sentence_at_position:
                                logger.info(f"位置对应的句子: {sentence_at_position[:50]}...")
                            else:
                                logger.warning(f"位置 ({para_idx+1}, {sent_idx+1}) 无效，找不到对应句子")
                                sentence_position = None  # 重置为None，表示位置无效
                        except Exception as e:
                            logger.error(f"解析句子位置时出错: {str(e)}")
                            sentence_position = None
                # 检查是否满足规则
                elif eval_result["score"] > 0:
                    # 如果评分大于0，说明文本满足规则
                    # 尝试找出具体匹配的句子
                    if len(text_to_evaluate) > 100:  # 假设长文本
                        matching_sentence = self.text_processor.find_matching_sentence_for_rule(
                            module_name, rule_index, text_to_evaluate
                        )
                        if matching_sentence:  # 添加空值检查
                            logger.info(f"从文本中找到的匹配句子: {matching_sentence[:50]}...")
                        else:
                            logger.info("从文本中未找到匹配句子")
                    else:
                        # 对于短文本，直接将其作为匹配句子
                        matching_sentence = text_to_evaluate
                        logger.info(f"短文本直接作为匹配句子: {text_to_evaluate[:50]}...")
                
                # 记录该规则评估的文本和匹配句子
                self.text_processor.set_rule_context(
                    module_name, rule_index, text_to_evaluate, matching_sentence
                )
                
                # 如果有匹配的句子和有效的位置信息，单独存储句子位置
                if matching_sentence:
                    # 如果这是依赖于其他规则的句子评估，且已经获取到源规则的位置信息
                    if depends_on_rule and hasattr(self.text_processor, 'rule_matching_sentence_positions') and \
                       module_name in self.text_processor.rule_matching_sentence_positions and \
                       depends_on_rule in self.text_processor.rule_matching_sentence_positions[module_name]:
                        # 使用源规则的位置信息
                        source_position = self.text_processor.rule_matching_sentence_positions[module_name][depends_on_rule]
                        self.text_processor.store_matching_sentence(
                            module_name, rule_index, matching_sentence, source_position
                        )
                        logger.info(f"规则 {rule_index} 继承规则 {depends_on_rule} 的句子位置: {source_position}")
                    elif sentence_position:
                        # 使用LLM返回的位置信息
                        self.text_processor.store_matching_sentence(
                            module_name, rule_index, matching_sentence, sentence_position
                        )
                        logger.info(f"成功存储规则 {rule_index} 的匹配句子及位置信息")
                    else:
                        # 仅存储句子内容，没有位置信息
                        self.text_processor.store_matching_sentence(
                            module_name, rule_index, matching_sentence
                        )
                        logger.info(f"成功存储规则 {rule_index} 的匹配句子（无位置信息）")
                
                # 检查评语是否包含通用评语关键词
                if "未提供需要评分的文本片段" in eval_result["comment"]:
                    generic_comment_counter["未提供需要评分的文本片段"] += 1
                    logger.error(f"⚠️ 添加通用评语: '{eval_result['comment']}' (第{generic_comment_counter['未提供需要评分的文本片段']}次)")
                
                # 添加评分结果
                self.score_manager.add_score(
                    rule_path, 
                    text_to_evaluate, 
                    eval_result["score"], 
                    eval_result["comment"],
                    eval_result.get("thought_process"),
                    text_range  # 添加范围描述
                )
                
                if self.verbose:
                    logger.info(f"评分结果：{eval_result['score']}分 - {eval_result['comment']}")
                    if matching_sentence:  # 添加空值检查
                        logger.info(f"匹配句子：{matching_sentence}")
                    else:
                        logger.info("没有匹配句子")
        
        # 在格式化前记录统计信息
        logger.info(f"格式化前评分结果总数: {len(self.score_manager.scores)}")
        logger.info(f"通用评语'未提供需要评分的文本片段'出现次数: {generic_comment_counter['未提供需要评分的文本片段']}")
        
        # 格式化评分后的文本
        scored_text = self.score_manager.format_scored_text(self.essay_text)
        
        # 如果启用了详细输出，打印评分摘要
        if self.verbose:
            logger.info("\n" + self.score_manager.get_score_summary())
        
        return scored_text
    
    def save_result(self, output_file: str):
        """
        保存评分结果到文件
        
        Args:
            output_file (str): 输出文件路径
        """
        scored_text = self.grade_essay()
        
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(scored_text)
            logger.info(f"评分结果已保存到 {output_file}")
        except Exception as e:
            logger.error(f"保存评分结果时出错: {str(e)}")
    
    def get_sentence_by_position(self, paragraph_index: int, sentence_index: int) -> str:
        """
        根据段落索引和句子索引获取句子
        
        Args:
            paragraph_index (int): 段落索引 (从0开始)
            sentence_index (int): 句子索引 (从0开始)
            
        Returns:
            str: 对应位置的句子，如果索引无效则返回空字符串
        """
        # 确保索引有效
        if 0 <= paragraph_index < len(self.sentences_by_para):
            paragraph_sentences = self.sentences_by_para[paragraph_index]
            if 0 <= sentence_index < len(paragraph_sentences):
                return paragraph_sentences[sentence_index]
            else:
                logger.warning(f"无效的句子索引: 第{paragraph_index+1}段没有第{sentence_index+1}句")
        else:
            logger.warning(f"无效的段落索引: {paragraph_index+1}，共有{len(self.sentences_by_para)}段")
        
        return ""
    
    def translate_position_to_description(self, paragraph_index: int, sentence_index: int) -> str:
        """
        将位置信息转换为描述性文本
        
        Args:
            paragraph_index (int): 段落索引 (从0开始)
            sentence_index (int): 句子索引 (从0开始)
            
        Returns:
            str: 位置描述，如"第2段第3句"
        """
        return f"第{paragraph_index+1}段第{sentence_index+1}句"
    
    def parse_sentence_position_description(self, position_desc: str, current_paragraph_index: int = 0) -> Tuple[int, int]:
        """
        解析句子位置描述，返回段落索引和句子索引
        
        Args:
            position_desc (str): 位置描述，如"第2句"或"该段第3句"
            current_paragraph_index (int): 当前处理的段落索引，用于相对位置描述
            
        Returns:
            Tuple[int, int]: (段落索引, 句子索引)，索引从0开始
        """
        # 中文数字映射
        chinese_num_map = {'一': 1, '二': 2, '三': 3, '四': 4, '五': 5, '六': 6, '七': 7, '八': 8, '九': 9, '十': 10}
        
        # 默认使用当前段落
        paragraph_index = current_paragraph_index
        sentence_index = 0  # 默认第一句
        
        # 简化的形式：如"第2句"，使用当前段落
        if re.match(r'^第\d+句$', position_desc):
            sent_match = re.search(r'第(\d+)句', position_desc)
            sentence_index = int(sent_match.group(1)) - 1  # 转为0-索引
            logger.info(f"解析位置描述 '{position_desc}': 使用当前段落 {paragraph_index+1}，句子索引 {sentence_index+1}")
            return (paragraph_index, sentence_index)
        
        # 中文数字形式：如"第三句"，使用当前段落
        if re.match(r'^第[一二三四五六七八九十]+句$', position_desc):
            sent_match = re.search(r'第([一二三四五六七八九十]+)句', position_desc)
            chinese_num = sent_match.group(1)
            if chinese_num in chinese_num_map:
                sentence_index = chinese_num_map[chinese_num] - 1  # 转为0-索引
                logger.info(f"解析位置描述 '{position_desc}': 使用当前段落 {paragraph_index+1}，句子索引 {sentence_index+1}")
                return (paragraph_index, sentence_index)
        
        # 包含段落和句子信息：如"第2段第3句"
        if re.match(r'^第\d+段第\d+句$', position_desc):
            para_match = re.search(r'第(\d+)段', position_desc)
            sent_match = re.search(r'第(\d+)句', position_desc)
            paragraph_index = int(para_match.group(1)) - 1  # 转为0-索引
            sentence_index = int(sent_match.group(1)) - 1  # 转为0-索引
            logger.info(f"解析位置描述 '{position_desc}': 段落索引 {paragraph_index+1}，句子索引 {sentence_index+1}")
            return (paragraph_index, sentence_index)
        
        # 包含段落和句子信息，中文数字形式：如"第一段第二句"
        if re.match(r'^第[一二三四五六七八九十]+段第[一二三四五六七八九十]+句$', position_desc):
            para_match = re.search(r'第([一二三四五六七八九十]+)段', position_desc)
            sent_match = re.search(r'第([一二三四五六七八九十]+)句', position_desc)
            
            para_chinese_num = para_match.group(1)
            sent_chinese_num = sent_match.group(1)
            
            if para_chinese_num in chinese_num_map and sent_chinese_num in chinese_num_map:
                paragraph_index = chinese_num_map[para_chinese_num] - 1  # 转为0-索引
                sentence_index = chinese_num_map[sent_chinese_num] - 1  # 转为0-索引
                logger.info(f"解析位置描述 '{position_desc}': 段落索引 {paragraph_index+1}，句子索引 {sentence_index+1}")
                return (paragraph_index, sentence_index)
        
        # 相对段落形式：如"该段第2句"或"本段第3句"
        if re.match(r'^(该|本)段第\d+句$', position_desc):
            sent_match = re.search(r'第(\d+)句', position_desc)
            sentence_index = int(sent_match.group(1)) - 1  # 转为0-索引
            logger.info(f"解析位置描述 '{position_desc}': 使用当前段落 {paragraph_index+1}，句子索引 {sentence_index+1}")
            return (paragraph_index, sentence_index)
        
        # 相对段落形式，中文数字：如"该段第二句"
        if re.match(r'^(该|本)段第[一二三四五六七八九十]+句$', position_desc):
            sent_match = re.search(r'第([一二三四五六七八九十]+)句', position_desc)
            sent_chinese_num = sent_match.group(1)
            
            if sent_chinese_num in chinese_num_map:
                sentence_index = chinese_num_map[sent_chinese_num] - 1  # 转为0-索引
                logger.info(f"解析位置描述 '{position_desc}': 使用当前段落 {paragraph_index+1}，句子索引 {sentence_index+1}")
                return (paragraph_index, sentence_index)
        
        # 未能匹配任何格式，默认使用当前段落的第一句
        logger.warning(f"无法解析位置描述 '{position_desc}'，使用默认: 段落 {paragraph_index+1}，句子 1")
        return (paragraph_index, 0)

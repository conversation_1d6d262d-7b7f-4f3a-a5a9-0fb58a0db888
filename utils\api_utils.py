"""
API工具模块。

提供API密钥管理、请求节流和错误处理功能。
"""
import os
import time
import random
import asyncio
from typing import List, Optional, Dict, Any, Union, Callable
from datetime import datetime

import httpx

from constants import DEFAULT_MODEL, DEFAULT_BASE_URL, DEFAULT_API_INTERVAL
from utils.logging import get_logger, ApiLogger


class RateLimiter:
    """
    API调用速率限制器。
    
    控制API调用频率，避免超出API提供商的限制。
    """
    
    def __init__(self, calls_per_minute: int = 60):
        """
        初始化速率限制器。
        
        Args:
            calls_per_minute: 每分钟允许的最大调用次数
        """
        self.interval = 60 / calls_per_minute
        self.last_call_time = 0
        self.semaphore = asyncio.Semaphore(1)
        self.logger = get_logger("rate_limiter")
    
    async def acquire(self) -> None:
        """
        获取调用许可。
        
        如果距离上次调用时间不足最小间隔，会等待适当时间。
        """
        async with self.semaphore:
            now = time.time()
            elapsed = now - self.last_call_time
            
            if elapsed < self.interval:
                wait_time = self.interval - elapsed
                self.logger.debug(f"速率限制: 等待 {wait_time:.2f} 秒")
                await asyncio.sleep(wait_time)
            
            self.last_call_time = time.time()
    
    def wait(self) -> None:
        """
        同步版本的获取调用许可。
        
        如果距离上次调用时间不足最小间隔，会等待适当时间。
        """
        now = time.time()
        elapsed = now - self.last_call_time
        
        if elapsed < self.interval:
            wait_time = self.interval - elapsed
            self.logger.debug(f"速率限制: 等待 {wait_time:.2f} 秒")
            time.sleep(wait_time)
        
        self.last_call_time = time.time()


class ApiKeyManager:
    """
    API密钥管理器。
    
    管理多个API密钥，支持轮换和随机选择。
    """
    
    def __init__(self, api_key_str: Optional[str] = None):
        """
        初始化API密钥管理器。
        
        Args:
            api_key_str: API密钥字符串，多个密钥用逗号分隔
        """
        self.api_keys: List[str] = []
        self.current_index = 0
        self.logger = get_logger("api_key_manager")
        
        # 从参数或环境变量加载API密钥
        if api_key_str:
            self.api_keys = self._parse_api_keys(api_key_str)
        elif "OPENAI_API_KEY" in os.environ:
            self.api_keys = self._parse_api_keys(os.environ["OPENAI_API_KEY"])
        
        # 随机选择起始索引，以便分散负载
        if self.api_keys:
            self.current_index = random.randint(0, len(self.api_keys) - 1)
            self.logger.debug(f"初始化API密钥管理器，共 {len(self.api_keys)} 个密钥")
    
    def _parse_api_keys(self, api_key_str: str) -> List[str]:
        """
        解析API密钥字符串。
        
        Args:
            api_key_str: API密钥字符串，多个密钥用逗号分隔
            
        Returns:
            API密钥列表
        """
        # 分割并清理密钥
        keys = [key.strip() for key in api_key_str.split(",")]
        # 移除空密钥
        return [key for key in keys if key]
    
    def get_current_key(self) -> str:
        """
        获取当前API密钥。
        
        Returns:
            当前API密钥
            
        Raises:
            ValueError: 没有可用的API密钥
        """
        if not self.api_keys:
            raise ValueError("没有可用的API密钥")
        
        return self.api_keys[self.current_index]
    
    def rotate_key(self) -> str:
        """
        轮换到下一个API密钥。
        
        Returns:
            新的当前API密钥
            
        Raises:
            ValueError: 没有可用的API密钥
        """
        if not self.api_keys:
            raise ValueError("没有可用的API密钥")
        
        # 更新索引
        self.current_index = (self.current_index + 1) % len(self.api_keys)
        self.logger.debug(f"轮换到下一个API密钥，当前索引: {self.current_index}")
        
        return self.get_current_key()
    
    def add_key(self, api_key: str) -> None:
        """
        添加新的API密钥。
        
        Args:
            api_key: 要添加的API密钥
        """
        if api_key and api_key not in self.api_keys:
            self.api_keys.append(api_key)
            self.logger.debug(f"添加新的API密钥，当前共 {len(self.api_keys)} 个密钥")


class OpenAIClient:
    """
    OpenAI API客户端。
    
    提供与OpenAI API交互的功能，包括API调用、错误处理和重试。
    """
    
    def __init__(
        self, 
        model_name: str = DEFAULT_MODEL,
        api_key: Optional[str] = None,
        base_url: Optional[str] = None,
        debug_log_file: str = "api_debug.log",
        calls_per_minute: int = 60
    ):
        """
        初始化OpenAI客户端。
        
        Args:
            model_name: 模型名称
            api_key: API密钥字符串，多个密钥用逗号分隔
            base_url: API基础URL
            debug_log_file: 调试日志文件名
            calls_per_minute: 每分钟最大调用次数
        """
        self.model_name = model_name
        self.base_url = base_url or os.environ.get("OPENAI_API_BASE", DEFAULT_BASE_URL)
        
        # 初始化API密钥管理器
        self.key_manager = ApiKeyManager(api_key)
        
        # 初始化速率限制器
        self.rate_limiter = RateLimiter(calls_per_minute)
        
        # 初始化日志记录器
        self.logger = get_logger("openai_client")
        self.api_logger = ApiLogger(debug_log_file)
        
        # 创建HTTP客户端
        self.client = httpx.Client(timeout=60.0)
    
    def call_api(
        self, 
        prompt: str, 
        temperature: float = 0.0,
        max_tokens: int = 2000,
        max_retries: int = 3
    ) -> str:
        """
        调用OpenAI API。
        
        Args:
            prompt: 提示文本
            temperature: 温度参数，控制随机性
            max_tokens: 最大生成令牌数
            max_retries: 最大重试次数
            
        Returns:
            API响应文本
            
        Raises:
            Exception: API调用失败
        """
        # 速率限制
        self.rate_limiter.wait()
        
        # 准备请求
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.key_manager.get_current_key()}"
        }
        
        json_data = {
            "model": self.model_name,
            "messages": [{"role": "user", "content": prompt}],
            "temperature": temperature,
            "max_tokens": max_tokens
        }
        
        # 执行API调用，带重试
        for attempt in range(max_retries + 1):
            try:
                self.logger.debug(f"调用API (尝试 {attempt+1}/{max_retries+1})")
                
                response = self.client.post(
                    self.base_url,
                    headers=headers,
                    json=json_data
                )
                
                # 检查状态码
                response.raise_for_status()
                
                # 解析响应
                json_response = response.json()
                response_text = json_response["choices"][0]["message"]["content"]
                
                return response_text
                
            except Exception as e:
                self.logger.warning(f"API调用错误 (尝试 {attempt+1}/{max_retries+1}): {str(e)}")
                
                # 最后一次尝试失败，抛出异常
                if attempt == max_retries:
                    raise
                
                # 尝试轮换API密钥
                if len(self.key_manager.api_keys) > 1:
                    self.key_manager.rotate_key()
                    self.logger.debug("轮换到下一个API密钥")
                
                # 等待重试
                time.sleep(2 ** attempt)  # 指数退避
    
    def close(self) -> None:
        """关闭HTTP客户端。"""
        self.client.close() 
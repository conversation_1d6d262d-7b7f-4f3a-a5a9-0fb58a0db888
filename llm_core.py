#!/usr/bin/env python
# -*- coding: utf-8 -*-

import json
import os
import re
import time
import logging
from typing import Dict, List, Any, Union
import traceback
import openai
import httpx
import random
from colorama import init, Fore, Style

# 从llm_config导入全局变量
from llm_config import logger, DEFAULT_MODEL, OPENAI_API_KEY, OPENAI_BASE_URL


class LLMInterface:
    """大模型接口：负责与大模型API交互"""
    
    def __init__(self, model_name=None, api_key=None, base_url=None, debug_log_file="api_debug.log"):
        """
        初始化大模型接口
        
        Args:
            model_name (str): 模型名称
            api_key (str): API密钥
            base_url (str): API基础URL
            debug_log_file (str): 调试日志文件路径
        """
        # 使用提供的模型名称或环境变量中的默认模型
        self.model_name = model_name if model_name else DEFAULT_MODEL
        self.debug_log_file = debug_log_file
        
        # 清空API调试日志文件
        try:
            # 以写模式打开文件（这会清空文件内容）
            with open(self.debug_log_file, 'w', encoding='utf-8') as f:
                f.write("")  # 写入空字符串，确保文件被清空
            logger.info(f"已清空API调试日志文件: {self.debug_log_file}")
        except Exception as e:
            logger.warning(f"清空API调试日志文件失败: {str(e)}")
        
        # 设置API密钥（优先级：参数 > 全局变量 > 环境变量）
        if api_key:
            self.api_keys = self._parse_api_keys(api_key)
        elif OPENAI_API_KEY:
            self.api_keys = self._parse_api_keys(OPENAI_API_KEY)
        else:
            env_api_key = os.getenv("OPENAI_API_KEY")
            if not env_api_key:
                raise ValueError("未提供API密钥，且环境变量中未设置OPENAI_API_KEY")
            self.api_keys = self._parse_api_keys(env_api_key)
        
        # 随机选择一个API密钥作为起始点，实现负载均衡
        if self.api_keys:
            self.current_key_index = random.randint(0, len(self.api_keys) - 1)
            logger.info(f"随机选择API密钥索引 {self.current_key_index} 作为起始点")
        else:
            self.current_key_index = 0
        
        # 设置API基础URL（优先级：参数 > 全局变量 > 默认值）
        if base_url:
            self.base_url = base_url
        elif OPENAI_BASE_URL:
            self.base_url = OPENAI_BASE_URL
        else:
            self.base_url = "https://api.openai.com/v1"
        
        # 配置OpenAI客户端 (使用当前API key)
        openai.api_key = self.get_current_api_key()
        openai.base_url = self.base_url
        
        # 获取API调用间隔时间（单位：秒）
        self.interval = int(os.getenv("INTERVAL", "0"))
        logger.info(f"设置API调用间隔：{self.interval}秒")
        
        # 初始化上次调用时间为0，确保第一次调用不需要等待
        self.last_api_call_time = 0
        
        # 存储大模型提取的文本范围结果缓存
        self.llm_extracted_texts = {}
        
        # 添加对于文本处理器的引用
        self.text_processor = None
    def _parse_api_keys(self, api_key_str: str) -> List[str]:
        """
        解析逗号分隔的多个API密钥
        
        Args:
            api_key_str (str): 逗号分隔的API密钥字符串
            
        Returns:
            List[str]: API密钥列表
        """
        # 分割并去除空格
        keys = [key.strip() for key in api_key_str.split(',')]
        # 过滤掉空字符串
        return [key for key in keys if key]
    
    def get_current_api_key(self) -> str:
        """
        获取当前API密钥
        
        Returns:
            str: 当前API密钥，如果没有API密钥则尝试从环境变量获取
        """
        if not self.api_keys:
            # 尝试从环境变量获取API密钥
            env_key = os.environ.get("OPENAI_API_KEY")
            if env_key:
                logger.info("使用环境变量中的API密钥")
                return env_key
            else:
                logger.error("没有可用的API密钥，也无法从环境变量获取")
                return "" # 返回空字符串，让后续调用API时失败，便于错误处理
        return self.api_keys[self.current_key_index]
    
    def rotate_to_next_api_key(self) -> str:
        """
        轮换到下一个API密钥
        
        Returns:
            str: 下一个API密钥，如果没有API密钥则返回None
        """
        if not self.api_keys:
            logger.warning("没有可用的API密钥，无法轮换")
            return None
        
        # 轮换到下一个索引
        self.current_key_index = (self.current_key_index + 1) % len(self.api_keys)
        
        # 获取新的API密钥
        api_key = self.api_keys[self.current_key_index]
        
        logger.info(f"轮换到下一个API密钥 (索引: {self.current_key_index + 1}/{len(self.api_keys)})")
        
        return api_key
def _call_openai(self, prompt: str) -> str:
        """
        调用OpenAI API获取响应
        
        Args:
            prompt (str): 提示词
            
        Returns:
            str: API返回的响应
            
        Raises:
            Exception: API调用失败时抛出异常
        """
        try:
            # 检查是否需要等待以满足API调用间隔要求
            if self.interval > 0 and self.last_api_call_time > 0:  # 只有当不是第一次调用时才等待
                # 直接使用配置的interval值
                wait_time = self.interval
                logger.info(f"等待API调用间隔：{wait_time}秒")
                
                # 简单倒计时输出
                print(f"\n[等待下一次API调用] {wait_time}秒", flush=True)
                
                # 逐秒倒计时
                for i in range(wait_time, 0, -1):
                    print(f"\r[等待下一次API调用] {i}秒... ", end="", flush=True)
                    time.sleep(1)
                
                # 完成倒计时
                print("\r[等待完成] 开始调用API...               ", flush=True)
            
            # 更新上次API调用时间
            self.last_api_call_time = time.time()
            
            logger.info(f"调用API - 模型: {self.model_name}, API密钥索引: {self.current_key_index + 1}")
            print(f"[API调用] 模型: {self.model_name}, 密钥索引: {self.current_key_index + 1}", flush=True)
            
            # 使用新版OpenAI API (>=1.0.0)
            # 设置更长的超时时间
            client = openai.OpenAI(
                api_key=self.get_current_api_key(),
                base_url=self.base_url,
                http_client=httpx.Client(
                    timeout=httpx.Timeout(60.0, connect=10.0),  # 设置更长的总超时和连接超时
                )
            )
            
            # 使用流式输出
            response_content = ""
            in_think_block = False  # 跟踪是否在<think>块内
            console_output = []  # 用于控制台显示
            
            try:
                stream = client.chat.completions.create(
                    model=self.model_name,
                    messages=[
                        {"role": "system", "content": "你是一位严格遵循评分标准的公务员考试申论评分专家。"},
                        {"role": "user", "content": prompt}
                    ],
                    temperature=0.1,  # 较低的温度以获得更一致的结果
                    max_tokens=4000,
                    stream=True  # 启用流式输出
                )
                
                # 处理流式响应
                for chunk in stream:
                    if chunk.choices and chunk.choices[0].delta.content:
                        content = chunk.choices[0].delta.content
                        
                        # 实时输出到控制台
                        print(content, end="", flush=True)
                        console_output.append(content)
                        
                        # 检查<think>标记
                        if "<think>" in content:
                            in_think_block = True
                        
                        # 检查</think>标记
                        if "</think>" in content:
                            in_think_block = False
                            # 不添加</think>标记本身到response_content
                            continue
                        
                        # 只有不在<think>块内的内容才添加到最终响应
                        if not in_think_block:
                            response_content += content
                
                # 控制台输出换行
                print()
                
                return response_content
            except httpx.HTTPStatusError as e:
                # 捕获HTTP状态错误并提供更明确的错误消息
                if e.response.status_code == 500:
                    error_msg = f"服务器内部错误 (500): 自建节点可能存在问题。URL: {self.base_url}, 模型: {self.model_name}"
                    logger.error(error_msg)
                    
                    # 输出更详细的诊断信息
                    print(Fore.RED + "===== 服务器内部错误 (500) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")
                    print(f"请求消息长度: {len(prompt)} 字符")
                    print(f"提示词前100字符: {prompt[:100]}...")
                    
                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")
                        
                    print(Fore.RED + "这可能是由于以下原因造成:")
                    print("1. 自建节点服务器负载过高")
                    print("2. 请求内容过长或包含模型无法处理的内容")
                    print("3. 自建节点与底层模型API连接问题")
                    print("4. 网络传输中断")
                    print(Style.RESET_ALL)
                    
                    raise Exception(error_msg)
                elif e.response.status_code == 429:
                    error_msg = f"请求频率过高 (429): API请求限制或配额不足。URL: {self.base_url}, 模型: {self.model_name}"
                    
                    # 输出更详细的诊断信息
                    print(Fore.RED + "===== 请求频率限制错误 (429) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")
                    
                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")
                    
                    print(Fore.RED + "这可能是由于以下原因造成:")
                    print("1. API密钥已达到请求配额限制")
                    print("2. 短时间内请求次数过多")
                    print("3. 自建节点设置了请求频率限制")
                    print(Style.RESET_ALL)
                    
                    raise Exception(error_msg)
                    
                elif e.response.status_code == 401 or e.response.status_code == 403:
                    error_msg = f"认证错误 ({e.response.status_code}): API密钥可能无效或没有权限。URL: {self.base_url}, 模型: {self.model_name}"
                    
                    # 输出更详细的诊断信息
                    print(Fore.RED + f"===== 认证错误 ({e.response.status_code}) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")
                    print(f"API密钥索引: {self.current_key_index + 1}/{len(self.api_keys) if self.api_keys else 1}")
                    
                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")
                    
                    print(Fore.RED + "这可能是由于以下原因造成:")
                    print("1. API密钥无效或过期")
                    print("2. 当前API密钥没有使用此模型的权限")
                    print("3. 自建节点认证配置错误")
                    print(Style.RESET_ALL)
                    
                    raise Exception(error_msg)
                else:
                    # 其他HTTP错误
                    error_msg = f"HTTP请求错误 ({e.response.status_code})"
                    
                    print(Fore.RED + f"===== HTTP错误 ({e.response.status_code}) =====")
                    print(f"请求URL: {self.base_url}")
                    print(f"使用模型: {self.model_name}")
                    
                    # 尝试获取响应内容
                    try:
                        response_text = e.response.text
                        print(f"错误响应: {response_text[:500]}")
                        logger.error(f"HTTP响应内容: {response_text}")
                    except:
                        print("无法获取错误响应内容")
                    
                    print(Style.RESET_ALL)
                    
                    raise Exception(f"{error_msg}: {e.response.text}")
            except httpx.ReadTimeout:
                raise Exception(f"API请求超时: 自建节点响应时间过长。URL: {self.base_url}, 模型: {self.model_name}")
            except httpx.ConnectTimeout:
                raise Exception(f"连接超时: 无法连接到API服务器。URL: {self.base_url}, 模型: {self.model_name}")
            except httpx.RequestError as e:
                raise Exception(f"网络请求错误: {str(e)}")
        except Exception as e:
            logger.error(f"OpenAI API调用失败 (API key索引 {self.current_key_index + 1}): {str(e)}")
            raise
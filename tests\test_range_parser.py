"""
范围解析器测试模块。
"""
import pytest
from typing import Dict, List, Any, TYPE_CHECKING
from unittest.mock import Mock, MagicMock

from constants import TextSeparators
from utils.range_parser import RangeParser, RangeNode

if TYPE_CHECKING:
    from _pytest.capture import CaptureFixture
    from _pytest.fixtures import FixtureRequest
    from _pytest.logging import LogC<PERSON>tureFixture
    from _pytest.monkeypatch import MonkeyPatch
    from pytest_mock.plugin import Mo<PERSON>Fixture


@pytest.fixture
def range_parser() -> RangeParser:
    """
    创建范围解析器实例。
    
    Returns:
        RangeParser实例
    """
    return RangeParser()


class TestRangeNode:
    """测试RangeNode数据类。"""
    
    def test_init_default_values(self) -> None:
        """测试默认值初始化。"""
        # 测试普通节点
        node = RangeNode(type="paragraph", value="首段")
        assert node.type == "paragraph"
        assert node.value == "首段"
        assert node.children is None
        
        # 测试复合节点自动初始化children
        seq_node = RangeNode(type="sequential")
        assert seq_node.type == "sequential"
        assert seq_node.value is None
        assert seq_node.children == []
        
        parallel_node = RangeNode(type="parallel")
        assert parallel_node.type == "parallel"
        assert parallel_node.value is None
        assert parallel_node.children == []
    
    def test_init_with_children(self) -> None:
        """测试带子节点初始化。"""
        child1 = RangeNode(type="paragraph", value="首段")
        child2 = RangeNode(type="sentence", value="第一句")
        
        parent = RangeNode(type="parallel", children=[child1, child2])
        
        assert parent.type == "parallel"
        assert len(parent.children) == 2
        assert parent.children[0].type == "paragraph"
        assert parent.children[1].type == "sentence"


class TestRangeParser:
    """测试范围解析器功能。"""
    
    def test_init_with_custom_separators(self) -> None:
        """测试自定义分隔符初始化。"""
        separators = TextSeparators(
            parallel_range_separators=["AND", "OR"],
            sequential_range_separators=["THEN"]
        )
        parser = RangeParser(separators)
        
        assert parser.separators.parallel_range_separators == ["AND", "OR"]
        assert parser.separators.sequential_range_separators == ["THEN"]
    
    @pytest.mark.parametrize("range_desc, expected_type", [
        ("全文", "full_text"),
        ("整篇作文", "full_text"),
        ("全部内容", "full_text"),
        ("标题", "title"),
        ("题目", "title"),
    ])
    def test_parse_keywords(self, range_parser: RangeParser, range_desc: str, expected_type: str) -> None:
        """
        测试关键词解析。
        
        Args:
            range_parser: 范围解析器实例
            range_desc: 范围描述
            expected_type: 预期的节点类型
        """
        node = range_parser.parse(range_desc)
        assert node.type == expected_type
    
    @pytest.mark.parametrize("range_desc, expected_type, expected_value", [
        ("首段", "paragraph", "首段"),
        ("第一段", "paragraph", "第一段"),
        ("第二段", "paragraph", "第二段"),
        ("尾段", "paragraph", "尾段"),
        ("最后一段", "paragraph", "最后一段"),
        ("第三段", "paragraph", "第三段"),
    ])
    def test_parse_paragraph(self, range_parser: RangeParser, range_desc: str, expected_type: str, expected_value: str) -> None:
        """
        测试段落解析。
        
        Args:
            range_parser: 范围解析器实例
            range_desc: 范围描述
            expected_type: 预期的节点类型
            expected_value: 预期的节点值
        """
        node = range_parser.parse(range_desc)
        assert node.type == expected_type
        assert node.value == expected_value
    
    @pytest.mark.parametrize("range_desc, expected_type, expected_value", [
        ("第一句", "sentence", "第一句"),
        ("第二句", "sentence", "第二句"),
        ("第十句", "sentence", "第十句"),
    ])
    def test_parse_sentence(self, range_parser: RangeParser, range_desc: str, expected_type: str, expected_value: str) -> None:
        """
        测试句子解析。
        
        Args:
            range_parser: 范围解析器实例
            range_desc: 范围描述
            expected_type: 预期的节点类型
            expected_value: 预期的节点值
        """
        node = range_parser.parse(range_desc)
        assert node.type == expected_type
        assert node.value == expected_value
    
    @pytest.mark.parametrize("range_desc, expected_type, expected_value", [
        ("该段第一句", "relative_sentence", "该段第一句"),
        ("本段第二句", "relative_sentence", "本段第二句"),
        ("该段第十句", "relative_sentence", "该段第十句"),
    ])
    def test_parse_relative_sentence(self, range_parser: RangeParser, range_desc: str, expected_type: str, expected_value: str) -> None:
        """
        测试相对句子解析。
        
        Args:
            range_parser: 范围解析器实例
            range_desc: 范围描述
            expected_type: 预期的节点类型
            expected_value: 预期的节点值
        """
        node = range_parser.parse(range_desc)
        assert node.type == expected_type
        assert node.value == expected_value
    
    @pytest.mark.parametrize("range_desc, expected_rule_num", [
        ("规则一的句子", 1),
        ("规则二句子", 2),
        ("规则十的句子", 10),
        ("规则3的句子", 3),
    ])
    def test_parse_rule_reference(self, range_parser: RangeParser, range_desc: str, expected_rule_num: int) -> None:
        """
        测试规则引用解析。
        
        Args:
            range_parser: 范围解析器实例
            range_desc: 范围描述
            expected_rule_num: 预期的规则编号
        """
        node = range_parser.parse(range_desc)
        assert node.type == "rule_reference"
        assert node.value == expected_rule_num
    
    def test_parse_parallel_range(self, range_parser: RangeParser) -> None:
        """
        测试并列范围解析。
        
        Args:
            range_parser: 范围解析器实例
        """
        range_desc = "首段;第二段;第三段"
        node = range_parser.parse(range_desc)
        
        assert node.type == "parallel"
        assert len(node.children) == 3
        assert node.children[0].type == "paragraph"
        assert node.children[0].value == "首段"
        assert node.children[1].type == "paragraph"
        assert node.children[1].value == "第二段"
        assert node.children[2].type == "paragraph"
        assert node.children[2].value == "第三段"
    
    def test_parse_sequential_range(self, range_parser: RangeParser) -> None:
        """
        测试递进范围解析。
        
        Args:
            range_parser: 范围解析器实例
        """
        range_desc = "首段,第一句"
        node = range_parser.parse(range_desc)
        
        assert node.type == "sequential"
        assert len(node.children) == 2
        assert node.children[0].type == "paragraph"
        assert node.children[0].value == "首段"
        assert node.children[1].type == "sentence"
        assert node.children[1].value == "第一句"
    
    def test_parse_complex_range(self, range_parser: RangeParser) -> None:
        """
        测试复杂范围解析。
        
        Args:
            range_parser: 范围解析器实例
        """
        range_desc = "首段,第一句;尾段,第二句"
        node = range_parser.parse(range_desc)
        
        assert node.type == "parallel"
        assert len(node.children) == 2
        
        # 第一个子范围：首段,第一句
        assert node.children[0].type == "sequential"
        assert len(node.children[0].children) == 2
        assert node.children[0].children[0].type == "paragraph"
        assert node.children[0].children[0].value == "首段"
        assert node.children[0].children[1].type == "sentence"
        assert node.children[0].children[1].value == "第一句"
        
        # 第二个子范围：尾段,第二句
        assert node.children[1].type == "sequential"
        assert len(node.children[1].children) == 2
        assert node.children[1].children[0].type == "paragraph"
        assert node.children[1].children[0].value == "尾段"
        assert node.children[1].children[1].type == "sentence"
        assert node.children[1].children[1].value == "第二句"
    
    def test_parse_invalid_range(self, range_parser: RangeParser) -> None:
        """
        测试无效范围的异常抛出。
        
        Args:
            range_parser: 范围解析器实例
        """
        with pytest.raises(ValueError):
            range_parser.parse("无效范围描述")


class TestExtractText:
    """测试文本提取功能。"""
    
    @pytest.fixture
    def mock_text_processor(self) -> Mock:
        """
        创建模拟的文本处理器。
        
        Returns:
            模拟的文本处理器对象
        """
        processor = Mock()
        processor.essay_text = "这是全文。"
        processor.title = "这是标题"
        processor.paragraphs = ["段落1", "段落2", "段落3"]
        processor.current_paragraph_index = 0
        processor.resolve_paragraph_reference = lambda ref: 0 if ref == "首段" else 2 if ref == "尾段" else 1
        processor.get_sentence_by_position = lambda p, s: f"段落{p+1}的第{s+1}句"
        processor.find_matching_sentence_for_rule = lambda module, rule, text: f"规则{rule}匹配的句子"
        
        return processor
    
    def test_extract_full_text(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取全文。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        node = RangeNode(type="full_text")
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "这是全文。"
    
    def test_extract_title(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取标题。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        node = RangeNode(type="title")
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "这是标题"
    
    def test_extract_paragraph(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取段落。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        node = RangeNode(type="paragraph", value="首段")
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "段落1"
        
        node = RangeNode(type="paragraph", value="尾段")
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "段落3"
    
    def test_extract_sentence(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取句子。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        node = RangeNode(type="sentence", value="第二句")
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "段落1的第2句"
    
    def test_extract_relative_sentence(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取相对句子。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        mock_text_processor.current_paragraph_index = 1
        node = RangeNode(type="relative_sentence", value="该段第三句")
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "段落2的第3句"
    
    def test_extract_rule_reference(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取规则引用。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        node = RangeNode(type="rule_reference", value=5)
        text = range_parser.extract_text(node, mock_text_processor, current_module="测试模块")
        assert text == "规则5匹配的句子"
        mock_text_processor.find_matching_sentence_for_rule.assert_called_with("测试模块", 5, mock_text_processor.essay_text)
    
    def test_extract_parallel(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取并列范围。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        child1 = RangeNode(type="paragraph", value="首段")
        child2 = RangeNode(type="paragraph", value="尾段")
        node = RangeNode(type="parallel", children=[child1, child2])
        
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "段落1\n段落3"
    
    def test_extract_sequential(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取递进范围。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        # 创建临时处理器的模拟
        temp_processor = Mock()
        temp_processor.current_paragraph_index = 0
        
        # 设置文本处理器类型的模拟，使其返回临时处理器
        mock_text_processor.__class__ = Mock(return_value=temp_processor)
        
        # 创建递进范围节点
        child1 = RangeNode(type="paragraph", value="首段")
        child2 = RangeNode(type="sentence", value="第一句")
        node = RangeNode(type="sequential", children=[child1, child2])
        
        # 当extract_text被调用时，对子节点进行不同的处理
        def side_effect(node, processor, *args, **kwargs):
            if node.type == "paragraph":
                return "段落1"
            elif node.type == "sentence":
                return "段落1的第1句"
        
        range_parser.extract_text = Mock(side_effect=side_effect)
        
        text = range_parser.extract_text(node, mock_text_processor)
        assert text == "段落1的第1句"
        
    def test_extract_unknown_type(self, range_parser: RangeParser, mock_text_processor: Mock) -> None:
        """
        测试提取未知类型的异常抛出。
        
        Args:
            range_parser: 范围解析器实例
            mock_text_processor: 模拟的文本处理器
        """
        node = RangeNode(type="unknown")
        with pytest.raises(ValueError):
            range_parser.extract_text(node, mock_text_processor) 
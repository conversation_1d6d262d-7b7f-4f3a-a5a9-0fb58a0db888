name: Python Tests

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main" ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.8", "3.9", "3.10"]

    steps:
    - uses: actions/checkout@v3
    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v4
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        python -m pip install ruff black pytest pytest-mock
        if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
    - name: Lint with ruff
      run: |
        ruff check .
    - name: Format check with black
      run: |
        black --check .
    - name: Test with pytest
      run: |
        pytest -v 